import React, {
  createContext,
  useState,
  useEffect,
  ReactNode,
  useCallback,
} from 'react';
import {
  User,
  login as loginApi,
  getMe,
  refreshAuthToken,
  logoutUser,
} from '@/services/auth.api';

interface AuthContextType {
  user: User | null;
  login: (credit: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export { AuthContext };

export const AuthProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Check if authentication should be skipped
  const skipAuth = import.meta.env.VITE_SKIP_AUTH === 'true';

  const logout = useCallback(async () => {
    try {
      if (!skipAuth) {
        await logoutUser();
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      if (!skipAuth) {
        localStorage.removeItem('authToken');
        localStorage.removeItem('refreshToken');
      }
      setUser(null);
      setError(null);
    }
  }, [skipAuth]);

  useEffect(() => {
    const initAuth = async () => {
      if (skipAuth) {
        // Create a mock user when skipping authentication
        setUser({
          uuid: 'dev-user-123',
          last_login: new Date().toISOString(),
          avatar: '',
          username: 'developer',
          first_name: 'Dev',
          last_name: 'User',
          email: '<EMAIL>',
          phone_number: '',
          registration_date: new Date().toISOString(),
          role: 'admin',
          groups: [],
          user_permissions: [],
        });
        setIsLoading(false);
        return;
      }

      const token = localStorage.getItem('authToken');
      if (token) {
        try {
          const userData = await getMe();
          setUser(userData);
        } catch {
          // Token is invalid, remove it
          localStorage.removeItem('authToken');
          localStorage.removeItem('refreshToken');
        }
      }
      setIsLoading(false);
    };
    initAuth();
  }, [skipAuth]);

  // Auto-refresh token before expiration
  useEffect(() => {
    if (!user || skipAuth) return;

    const refreshInterval = setInterval(
      async () => {
        const refreshToken = localStorage.getItem('refreshToken');
        if (refreshToken) {
          try {
            const newToken = await refreshAuthToken(refreshToken);
            localStorage.setItem('authToken', newToken);
          } catch {
            // Refresh failed, logout user
            await logout();
          }
        }
      },
      14 * 60 * 1000
    ); // Refresh every 14 minutes

    return () => clearInterval(refreshInterval);
  }, [user, skipAuth, logout]);

  const login = async (credit: string, password: string) => {
    try {
      setError(null);

      if (skipAuth) {
        // Mock login when skipping authentication
        setUser({
          uuid: 'dev-user-123',
          last_login: new Date().toISOString(),
          avatar: '',
          username: credit || 'developer',
          first_name: 'Dev',
          last_name: 'User',
          email: '<EMAIL>',
          phone_number: '',
          registration_date: new Date().toISOString(),
          role: 'admin',
          groups: [],
          user_permissions: [],
        });
        return;
      }

      const response = await loginApi(credit, password);

      if (!response.access) {
        throw new Error('No access token received from server');
      }

      if (!response.refresh) {
        throw new Error('No refresh token received from server');
      }

      const { access, refresh } = response;

      localStorage.setItem('authToken', access);
      localStorage.setItem('refreshToken', refresh);

      const userInfo = await getMe();
      setUser(userInfo);
    } catch (err) {
      console.error('Login error:', err);
      const errorMessage =
        err instanceof Error ? err.message : 'خطا در ورود به سامانه';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        login,
        logout,
        isLoading,
        isAuthenticated: !!user,
        error,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
