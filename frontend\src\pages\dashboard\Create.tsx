import Stepper from '@/components/ui/Stepper';
import { useState, useCallback, useMemo, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import Breadcrumb from '@/components/ui/Breadcrumb';
import TextInput from '@/components/ui/TextInput';
import TextArea from '@/components/ui/TextArea';
import Button from '@/components/ui/Button';
import { FileText } from 'lucide-react';
import { CheckIcon } from '@phosphor-icons/react';
import { motion, AnimatePresence } from 'framer-motion';
import SearchDrawerInput from '@/components/ui/SearchDrawerInput';
import TimePeriodPicker from '@/components/ui/TimePeriodPicker';
import * as z from 'zod';
import TagInput from '@/components/ui/TagInput';
import { User } from '@/components/ui/UserCard';
import { createDashboard } from '@/services/dashboardService';
import { CreateDashboardPayload } from '@/types/dashboard';

interface Form2 {
  params: {
    runtime: {
      gap: number;
    };
    query: {
      q: string;
      hashtags?: string[];
      sources?: string[];
    };
  };
}

// Sample source data
const sampleSources: User[] = [
  {
    id: '1',
    name: 'خبرگزاری ایسنا',
    username: 'isna',
    avatar: 'https://images.unsplash.com/photo-1504711434969-e33886168f5c?w=150&h=150&fit=crop&crop=face',
    isOnline: true,
    lastSeen: new Date(Date.now() - 5 * 60 * 1000).toISOString(), // 5 minutes ago
    bio: 'خبرگزاری دانشجویان ایران - ارائه اخبار روز و تحلیل‌های سیاسی، اجتماعی و فرهنگی',
    email: '<EMAIL>',
    website: 'https://www.isna.ir',
    location: 'تهران، ایران',
    joinDate: '2010-03-15',
    stats: {
      posts: 45230,
      followers: 2500000,
      following: 1250,
      articles: 12500,
    },
  },
  {
    id: '2',
    name: 'خبرگزاری تسنیم',
    username: 'tasnim',
    avatar: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=150&h=150&fit=crop&crop=face',
    isOnline: true,
    lastSeen: new Date().toISOString(),
    bio: 'خبرگزاری بین‌المللی تسنیم - پوشش اخبار داخلی و بین‌المللی با نگاه تحلیلی',
    email: '<EMAIL>',
    website: 'https://www.tasnimnews.com',
    location: 'تهران، ایران',
    joinDate: '2012-07-20',
    stats: {
      posts: 38750,
      followers: 1800000,
      following: 980,
      articles: 9800,
    },
  },
  {
    id: '3',
    name: 'خبرگزاری فارس',
    username: 'fars',
    avatar: 'https://images.unsplash.com/photo-1586297135537-94bc9ba060aa?w=150&h=150&fit=crop&crop=face',
    isOnline: false,
    lastSeen: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
    bio: 'خبرگزاری فارس - پیشگام در ارائه اخبار فوری و گزارش‌های میدانی از سراسر کشور',
    email: '<EMAIL>',
    website: 'https://www.farsnews.ir',
    location: 'تهران، ایران',
    joinDate: '2003-11-10',
    stats: {
      posts: 52100,
      followers: 3200000,
      following: 1500,
      articles: 15600,
    },
  },
  {
    id: '4',
    name: 'خبرگزاری مهر',
    username: 'mehr',
    avatar: 'https://images.unsplash.com/photo-1551836022-deb4988cc6c0?w=150&h=150&fit=crop&crop=face',
    isOnline: true,
    lastSeen: new Date(Date.now() - 10 * 60 * 1000).toISOString(), // 10 minutes ago
    bio: 'خبرگزاری مهر - تولید محتوای خبری با تمرکز بر اقتصاد، فناوری و علم',
    email: '<EMAIL>',
    website: 'https://www.mehrnews.com',
    location: 'تهران، ایران',
    joinDate: '2003-05-25',
    stats: {
      posts: 41800,
      followers: 2100000,
      following: 1100,
      articles: 11200,
    },
  },
  {
    id: '5',
    name: 'خبرگزاری ایرنا',
    username: 'irna',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    isOnline: true,
    lastSeen: new Date().toISOString(),
    bio: 'خبرگزاری جمهوری اسلامی ایران - رسمی‌ترین منبع اخبار دولتی و سیاسی کشور',
    email: '<EMAIL>',
    website: 'https://www.irna.ir',
    location: 'تهران، ایران',
    joinDate: '1934-06-01',
    stats: {
      posts: 67500,
      followers: 4500000,
      following: 2000,
      articles: 22000,
    },
  },
  {
    id: '6',
    name: 'خبرگزاری برنا',
    username: 'borna',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    isOnline: false,
    lastSeen: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
    bio: 'خبرگزاری برنا - تخصصی در حوزه اقتصاد، بازار سرمایه و تجارت',
    email: '<EMAIL>',
    website: 'https://www.borna.news',
    location: 'تهران، ایران',
    joinDate: '2008-09-12',
    stats: {
      posts: 28900,
      followers: 950000,
      following: 650,
      articles: 7800,
    },
  },
];

export default function CreateDashboard() {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(0);
  const [form1, setForm1] = useState({ title: '', description: '' });
  const [form2, setForm2] = useState<Form2>({
    params: { runtime: { gap: 24 * 60 * 60 * 1000 }, query: { q: '' } }, // 24 hours in milliseconds
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [createdDashboard, setCreatedDashboard] = useState<any>(null);
  const lastGapValueRef = useRef<number>(12 * 60 * 60 * 1000);

  const steps = [
    {
      title: 'عنوان داشبورد',
    },
    {
      title: 'تنظیمات فیلتر',
    },
    {
      title: 'تایید نهایی',
    },
  ];

  const breadcrumbItems = [
    { label: 'داشبورد', href: '/dashboard' },
    { label: 'ایجاد داشبورد جدید' },
  ];

  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      const { name, value } = e.target;
      if (name === 'title' || name === 'description') {
        setForm1((prev) => ({ ...prev, [name]: value }));
        setErrors((prev) => ({ ...prev, [name]: '' }));
      } else if (name === 'searchQuery') {
        setForm2((prev) => ({
          ...prev,
          params: {
            ...prev.params,
            query: {
              ...prev.params.query,
              q: value,
            },
          },
        }));
        setErrors((prev) => ({ ...prev, [name]: '' }));
      }
    },
    []
  );

  const handleHashtagsChange = useCallback((newTags: string[]) => {
    setForm2((prev) => ({
      ...prev,
      params: {
        ...prev.params,
        query: {
          ...prev.params.query,
          hashtags: newTags,
        },
      },
    }));
  }, []);

  const handleSourcesChange = useCallback((newTags: string[]) => {
    setForm2((prev) => ({
      ...prev,
      params: {
        ...prev.params,
        query: {
          ...prev.params.query,
          sources: newTags,
        },
      },
    }));
  }, []);

  const handleTimePeriodChange = useCallback((gap: number) => {
    console.log('TimePeriodPicker onChange called with:', gap, 'milliseconds');
    console.log('Last gap value from ref:', lastGapValueRef.current);

    // Add a small tolerance for floating point comparison
    const tolerance = 1000; // 1 second tolerance

    // Only update if the value is actually different (with tolerance) to prevent infinite loops
    if (Math.abs(lastGapValueRef.current - gap) > tolerance) {
      console.log('Updating gap from', lastGapValueRef.current, 'to', gap);
      lastGapValueRef.current = gap;

      setForm2((prev) => ({
        ...prev,
        params: {
          ...prev.params,
          runtime: {
            ...prev.params.runtime,
            gap,
          },
        },
      }));
    } else {
      console.log('Gap value unchanged (within tolerance), skipping update');
    }
  }, []);

  const form1Schema = z.object({
    title: z
      .string()
      .min(3, { message: 'عنوان باید حداقل ۳ کاراکتر داشته باشد.' }),
    description: z.string().optional(),
  });

  const form2Schema = z.object({
    params: z.object({
      runtime: z.object({
        gap: z.number().positive({ message: 'بازه زمانی باید مثبت باشد.' }),
      }),
      query: z.object({
        q: z.string().min(2, { message: 'عبارت جستجو الزامی است.' }),
        hashtags: z.array(z.string()).optional(),
        sources: z.array(z.string()).optional(),
      }),
    }),
  });

  const handleCreateDashboard = async () => {
    setIsSubmitting(true);
    setErrors({});

    try {
      // Generate a preview based on the form data
      // const preview = `داشبورد ${form1.title} - جستجو: ${form2.params.query.q}${
      //   form2.params.query.hashtags && form2.params.query.hashtags.length > 0
      //     ? ` - هشتگ‌ها: ${form2.params.query.hashtags.join(', ')}`
      //     : ''
      // }`;

      // Combine form1 and form2 data into the payload
      const payload: CreateDashboardPayload = {
        title: form1.title,
        description: form1.description || undefined,
        params: form2.params,
      };

      // Create the dashboard
      const dashboard = await createDashboard(payload);
      setCreatedDashboard(dashboard);
      setCurrentStep(2);
    } catch (error) {
      console.error('Dashboard creation error:', error);
      const errorMessage =
        error instanceof Error ? error.message : 'خطا در ایجاد داشبورد';

      // Handle Zod validation errors by mapping them to form fields
      if (error instanceof z.ZodError) {
        const errorMap: Record<string, string> = {};
        error.issues.forEach((issue) => {
          const path = issue.path.join('.');
          if (path.includes('title')) {
            errorMap['title'] = issue.message;
          } else if (path.includes('description')) {
            errorMap['description'] = issue.message;
          } else if (path.includes('q')) {
            errorMap['searchQuery'] = issue.message;
          } else {
            errorMap['general'] = issue.message;
          }
        });
        setErrors(errorMap);
      } else {
        setErrors({ general: errorMessage });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleNext = () => {
    if (currentStep === 0) {
      const result = form1Schema.safeParse(form1);

      if (!result.success) {
        const errorMap: Record<string, string> = {};
        result.error.issues.forEach((issue) => {
          const field = String(issue.path[0]);
          errorMap[field] = issue.message;
        });
        setErrors(errorMap);
        return;
      }

      setCurrentStep(1);
    } else if (currentStep === 1) {
      const result = form2Schema.safeParse(form2);

      if (!result.success) {
        const errorMap: Record<string, string> = {};
        result.error.issues.forEach((issue) => {
          const path = issue.path.join('.');
          if (path.includes('q')) {
            errorMap['searchQuery'] = issue.message;
          } else {
            errorMap[String(issue.path[0])] = issue.message;
          }
        });
        setErrors(errorMap);
        return;
      }

      // Instead of going to step 2, trigger dashboard creation
      handleCreateDashboard();
    }
  };

  const handleBack = useCallback(() => {
    setCurrentStep((prev) => Math.max(prev - 1, 0));
  }, []);

  const handleCancel = useCallback(() => {
    window.history.back();
  }, []);

  const handleNavigateToCreate = useCallback(() => {
    navigate('/dashboard/create');
  }, [navigate]);

  const handleNavigateToDashboard = useCallback(() => {
    navigate('/dashboard');
  }, [navigate]);

  const handleNavigateToCreateReport = useCallback(() => {
    if (createdDashboard) {
      navigate(`/dashboard/${createdDashboard.id}/create-report`);
    }
  }, [navigate, createdDashboard]);

  // Memoize the time period value to prevent unnecessary re-renders
  const timePeriodValue = useMemo(() => {
    // Update the ref when the value changes from other sources
    lastGapValueRef.current = form2.params.runtime.gap;
    return form2.params.runtime.gap;
  }, [form2.params.runtime.gap]);

  return (
    <div className="min-h-full p-8">
      <Breadcrumb items={breadcrumbItems} />
      <div className="mx-auto mt-8 flex w-full max-w-7xl flex-col items-center">
        <h1 className="text-4xl font-bold text-white">ایجاد داشبورد جدید</h1>
        <div className="my-4 h-[2px] w-1/12 bg-gray-300"></div>

        <Stepper steps={steps} currentStep={currentStep} className="mt-8" />

        <div className="relative mt-8 w-full pb-8">
          <AnimatePresence mode="wait">
            {currentStep === 0 && (
              <motion.div
                key="step1"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.3 }}
                className="mx-auto w-full max-w-2xl space-y-6"
              >
                <TextInput
                  label="عنوان داشبورد"
                  name="title"
                  value={form1.title}
                  onChange={handleChange}
                  placeholder="عنوان داشبورد را وارد کنید"
                  error={errors.title}
                />
                <TextArea
                  label="توضیحات"
                  name="description"
                  value={form1.description}
                  onChange={handleChange}
                  placeholder="متن مورد نظر خود را بنویسید"
                  error={errors.description}
                />
                <div className="flex flex-col gap-2 sm:flex-row">
                  <div className="flex-grow"></div>
                  <Button variant="secondary" onClick={handleCancel}>
                    انصراف
                  </Button>
                  <Button
                    onClick={handleNext}
                    icon={<FileText className="h-4 w-4" />}
                    iconPosition="start"
                  >
                    ثبت و ادامه
                  </Button>
                </div>
              </motion.div>
            )}

            {currentStep === 1 && (
              <motion.div
                key="step2"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.3 }}
                className="mx-auto w-full max-w-4xl space-y-6"
              >
                <SearchDrawerInput
                  label="عبارت جستجو"
                  name="searchQuery"
                  placeholder="عبارت جستجو خود را وارد کنید"
                  value={form2.params.query.q}
                  onChange={handleChange}
                  error={errors.searchQuery}
                />

                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  <TagInput
                    label="هشتگ‌ها"
                    mode="scroll"
                    name="hashtags"
                    value={form2.params.query.hashtags}
                    onChange={handleHashtagsChange}
                    placeholder="هشتگ‌های مورد نظر را وارد کنید"
                  />

                  <TagInput
                    label="منابع"
                    mode="scroll"
                    inputMode="select"
                    name="sources"
                    value={form2.params.query.sources}
                    onChange={handleSourcesChange}
                    users={sampleSources}
                    showMoreEnabled={true}
                    modalTitle="انتخاب منابع خبری"
                    searchPlaceholder="جستجو در منابع..."
                    placeholder="منابع خبری را انتخاب کنید"
                  />
                </div>

                <TimePeriodPicker
                  value={timePeriodValue}
                  onChange={handleTimePeriodChange}
                  label="بازه زمانی نتایج گزارش"
                />

                <div className="h-[1px] bg-neutral-700"></div>

                {errors.general && (
                  <div className="rounded-md bg-red-50 p-4">
                    <div className="text-sm text-red-700">{errors.general}</div>
                  </div>
                )}

                <div className="flex flex-col gap-2 sm:flex-row">
                  <Button
                    variant="secondary"
                    onClick={handleBack}
                    disabled={isSubmitting}
                  >
                    قبلی
                  </Button>
                  <div className="flex-grow"></div>
                  <Button
                    variant="secondary"
                    onClick={handleCancel}
                    disabled={isSubmitting}
                  >
                    انصراف
                  </Button>
                  <Button
                    onClick={handleNext}
                    icon={<FileText className="h-4 w-4" />}
                    iconPosition="start"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? 'در حال ایجاد...' : 'ایجاد داشبورد'}
                  </Button>
                </div>
              </motion.div>
            )}

            {currentStep === 2 && (
              <motion.div
                key="step3"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.3 }}
                className="mx-auto w-full max-w-4xl space-y-6"
              >
                <div className="flex flex-col items-center">
                  <div className="bg-primary-500 flex h-16 w-16 items-center justify-center rounded-full">
                    <CheckIcon size={40} color="#FFFFFF" />
                  </div>

                  <div className="mt-8 text-[25px] font-bold text-white">
                    داشبورد گزارشات با موفقیت ایجاد شد
                  </div>

                  <div className="mt-6 text-xl font-medium text-stone-300">
                    {createdDashboard ? (
                      <>
                        داشبورد شما با عنوان "{createdDashboard.title}" و عبارت
                        جستجوی "{form2.params.query.q}" ایجاد شد.
                        {form2.params.query.hashtags && form2.params.query.hashtags.length > 0 && (
                          <div className="mt-2 text-lg">
                            هشتگ‌ها: {form2.params.query.hashtags.join(', ')}
                          </div>
                        )}
                        {form2.params.query.sources && form2.params.query.sources.length > 0 && (
                          <div className="mt-2 text-lg">
                            منابع: {form2.params.query.sources.join(', ')}
                          </div>
                        )}
                        {form1.description && (
                          <div className="mt-2 text-lg">
                            توضیحات: {form1.description}
                          </div>
                        )}
                      </>
                    ) : (
                      'داشبورد شما با موفقیت ایجاد شد.'
                    )}
                  </div>

                  <div className="mt-10 flex flex-row gap-4">
                    <Button
                      variant="secondary"
                      onClick={handleNavigateToCreate}
                    >
                      ایجاد داشبورد جدید
                    </Button>
                    <Button
                      variant="primary"
                      onClick={handleNavigateToDashboard}
                    >
                      بازگشت به داشبورد
                    </Button>
                    {createdDashboard && (
                      <Button
                        variant="primary"
                        onClick={handleNavigateToCreateReport}
                      >
                        ایجاد گزارش
                      </Button>
                    )}
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
}
