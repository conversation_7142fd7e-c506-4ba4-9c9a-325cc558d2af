import { motion } from 'framer-motion';
import { Check } from 'lucide-react';
import { cn } from '@/utils/utlis';

export interface User {
  id: string;
  name: string;
  username: string;
  avatar: string;
  isSelected?: boolean;
  isOnline?: boolean;
  lastSeen?: string;
  bio?: string;
  email?: string;
  phone?: string;
  website?: string;
  location?: string;
  joinDate?: string;
  stats?: {
    posts?: number;
    followers?: number;
    following?: number;
    articles?: number;
  };
}

interface UserCardProps {
  user: User;
  onSelect: (user: User) => void;
  onViewDetails?: (user: User) => void;
  className?: string;
}

export default function UserCard({ user, onSelect, onViewDetails, className }: UserCardProps) {
  const handleSelectClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    onSelect(user);
  };

  const handleCardClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onViewDetails) {
      onViewDetails(user);
    }
  };

  return (
    <motion.div
      className={cn(
        'relative cursor-pointer rounded-lg border-2 bg-gradient-to-br from-gray-800/50 to-gray-900/50 p-4 transition-all duration-300',
        user.isSelected
          ? 'border-primary-400 bg-gradient-to-br from-primary-400/10 to-primary-500/5 shadow-lg shadow-primary-400/20'
          : 'border-gray-600/50 hover:border-primary-400/50 hover:shadow-md hover:shadow-primary-400/10',
        className
      )}
      onClick={handleCardClick}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ type: 'spring', stiffness: 300, damping: 25 }}
    >
      {/* Selection button */}
      <motion.button
        className={cn(
          "absolute top-1 right-1 z-10 flex h-6 w-6 items-center justify-center rounded-full border-2 transition-all duration-200",
          user.isSelected
            ? "bg-primary-400 border-primary-400 shadow-lg"
            : "bg-gray-700 border-gray-600 hover:border-primary-400/50"
        )}
        onClick={handleSelectClick}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        initial={{ scale: 0, rotate: -180 }}
        animate={{ scale: 1, rotate: 0 }}
        transition={{ type: 'spring', stiffness: 400, damping: 20 }}
      >
        {user.isSelected ? (
          <Check size={14} className="text-black" />
        ) : (
          <div className="h-2 w-2 rounded-full bg-gray-400" />
        )}
      </motion.button>

      {/* User Avatar */}
      <div className="relative mb-3 flex justify-center">
        <div className="relative">
          <div
            className={cn(
              'h-16 w-16 rounded-full border-2 bg-gradient-to-br from-gray-700 to-gray-800 p-0.5',
              user.isSelected
                ? 'border-primary-400 shadow-lg shadow-primary-400/30'
                : 'border-gray-600'
            )}
          >
            <img
              src={user.avatar}
              alt={user.name}
              className="h-full w-full rounded-full object-cover"
            />
          </div>
          
          {/* Online status indicator */}
          {user.isOnline && (
            <div className="absolute -bottom-1 -right-1 h-4 w-4 rounded-full border-2 border-gray-800 bg-green-400"></div>
          )}
        </div>
      </div>

      {/* User Info */}
      <div className="text-center">
        <h3 className="mb-1 text-sm font-semibold text-white">{user.name}</h3>
        <p className="text-xs text-gray-400">@{user.username}</p>
      </div>

      {/* Futuristic border effect */}
      <div
        className={cn(
          'absolute inset-0 rounded-lg opacity-0 transition-opacity duration-300',
          'bg-gradient-to-r from-transparent via-primary-400/20 to-transparent',
          'hover:opacity-100'
        )}
        style={{
          background: user.isSelected
            ? 'linear-gradient(45deg, transparent, rgba(91, 247, 250, 0.1), transparent)'
            : undefined,
        }}
      />
    </motion.div>
  );
}
