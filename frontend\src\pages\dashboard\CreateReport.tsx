import { useParams } from 'react-router-dom';
import Breadcrumb from '@/components/ui/Breadcrumb';
import SocialMediaSelect from '@/components/ui/SocialMediaSelect';
import { useState } from 'react';
import Select, { SelectOption } from '@/components/ui/Select';
import TextInput from '@/components/ui/TextInput';

export default function CreateReport() {
  const { id } = useParams<{ id: string }>();
  const [selectedSocials, setSelectedSocials] = useState<number[]>([1]);
  const [timeout, setTimeout] = useState<string>('');
  const [error, setError] = useState<string>('');

  const breadcrumbItems = [
    { label: 'داشبورد', href: '/dashboard' },
    { label: 'داشبورد جزئیات', href: `/dashboard/${id}` },
    { label: 'ایجاد گزارش جدید' },
  ];

  const handleSocialMediaSelectChange = (selectedIds: number[]) => {
    setSelectedSocials(selectedIds);
    setError('');
  };

  const timeoutOptions: SelectOption[] = [
    { label: 'هر نیم ساعت یکبار', value: '1800000' },
    { label: 'هر یک ساعت یکبار', value: '7200000' },
    {
      label: 'هر دو ساعت یکبار',
      value: '3600000',
    },
  ];

  return (
    <div className="min-h-full p-8">
      <Breadcrumb items={breadcrumbItems} />
      <div className="mx-auto mt-8 w-full max-w-7xl space-y-6">
        {/* Social Media Select - Full Width */}
        <div className="w-full">
          <SocialMediaSelect
            label="بستر‌های مورد نظر خود را انتخاب کنید"
            value={selectedSocials}
            onChange={handleSocialMediaSelectChange}
            error={error}
          />
        </div>

        {/* Text Input and Select - Half Width Each */}
        <div className="mt-8 flex items-center gap-4">
          <div className="flex-6">
            <TextInput
              label="عنوان نمودار"
              placeholder="نمودار برترین محتوا منتشر شده در اینستاگرام"
            />
          </div>
          <div className="flex-6">
            <Select
              label="وقفه زمانی به‌روزرسانی گزارش‌ها"
              placeholder="هر دو ساعت یکبار"
              options={timeoutOptions}
              value={timeout}
              onChange={(value) =>
                setTimeout(Array.isArray(value) ? value[0] : value)
              }
            />
          </div>
        </div>

        {/* Full-width Select on a new line */}
        <div className="flex items-center gap-4">
          <div className="flex-6">
            <Select
              label="گزارش‌های آماری و هوش مصنوعی"
              placeholder="برترین محتوا منتشر شده"
              options={timeoutOptions}
              value={timeout}
              onChange={(value) =>
                setTimeout(Array.isArray(value) ? value[0] : value)
              }
            />
          </div>
          <div className="flex-6">
            <Select
              label="نوع نمدار"
              placeholder="نمودار میله‌ای"
              options={timeoutOptions}
              value={timeout}
              onChange={(value) =>
                setTimeout(Array.isArray(value) ? value[0] : value)
              }
            />
          </div>
        </div>
      </div>
    </div>
  );
}
