import React, { useRef } from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { useChartResize } from '@/hooks/useChartResize';

interface ResponsiveChartWrapperProps {
  /**
   * Highcharts options configuration
   */
  options: Highcharts.Options;
  /**
   * Additional CSS classes for the wrapper container
   */
  className?: string;
  /**
   * Minimum height for the chart in pixels
   * @default 200
   */
  minHeight?: number;
  /**
   * Maximum height for the chart in pixels
   */
  maxHeight?: number;
  /**
   * Debounce delay for resize events in milliseconds
   * @default 100
   */
  debounceDelay?: number;
  /**
   * Whether to maintain aspect ratio when resizing
   * @default false
   */
  maintainAspectRatio?: boolean;
  /**
   * Custom container props to pass to HighchartsReact
   */
  containerProps?: React.HTMLAttributes<HTMLDivElement>;
  /**
   * Optional title to display above the chart
   */
  title?: string;
  /**
   * Custom title component or element
   */
  titleComponent?: React.ReactNode;
  /**
   * Whether to show the default wrapper styling
   * @default true
   */
  showWrapper?: boolean;
}

/**
 * A responsive wrapper component for Highcharts that automatically
 * adjusts chart size based on container dimensions
 */
const ResponsiveChartWrapper: React.FC<ResponsiveChartWrapperProps> = ({
  options,
  className = '',
  minHeight = 200,
  maxHeight,
  debounceDelay = 100,
  maintainAspectRatio = false,
  containerProps = {},
  title,
  titleComponent,
  showWrapper = true,
}) => {
  const chartRef = useRef<HighchartsReact.RefObject>(null);
  
  // Use the custom hook for responsive chart resizing
  const { containerRef } = useChartResize(chartRef, {
    minHeight,
    maxHeight,
    debounceDelay,
    maintainAspectRatio,
  });

  // Ensure chart options don't have fixed height/width to allow dynamic resizing
  const responsiveOptions: Highcharts.Options = {
    ...options,
    chart: {
      ...options.chart,
      // Remove fixed dimensions to allow dynamic resizing
      height: undefined,
      width: undefined,
    },
  };

  const chartElement = (
    <HighchartsReact
      ref={chartRef}
      highcharts={Highcharts}
      options={responsiveOptions}
      containerProps={{
        style: {
          height: '100%',
          width: '100%',
        },
        ...containerProps,
      }}
    />
  );

  if (!showWrapper) {
    return (
      <div ref={containerRef} className={className}>
        {titleComponent}
        {title && !titleComponent && (
          <div className="p-3 text-white">{title}</div>
        )}
        {chartElement}
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className={`h-full w-full rounded-[5px] border border-[#323538] bg-[#181A1B] p-2 ${className}`}
    >
      {titleComponent}
      {title && !titleComponent && (
        <div className="p-3 text-white">{title}</div>
      )}
      <div className={title || titleComponent ? 'h-[calc(100%-148px)]' : 'h-full'}>
        {chartElement}
      </div>
    </div>
  );
};

export default ResponsiveChartWrapper;
