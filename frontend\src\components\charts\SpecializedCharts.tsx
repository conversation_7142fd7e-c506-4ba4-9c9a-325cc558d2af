import React, { memo, useRef, useEffect } from 'react';
import ResponsiveChartWrapper from './ResponsiveChartWrapper';
import { createChartConfig, RESPONSIVE_RULES } from './chartConfig';
import { useForceGraphResize } from '@/hooks/useForceGraphResize';
import Highcharts from 'highcharts';
import HighchartsMap from 'highcharts/modules/map';
import HighchartsWordCloud from 'highcharts/modules/wordcloud';
import ForceGraph2D, { ForceGraphMethods } from 'react-force-graph-2d';
import worldMap from '@highcharts/map-collection/custom/world.topo.json';
import iranMap from '@highcharts/map-collection/countries/ir/ir-all.geo.json';

HighchartsMap(Highcharts);
HighchartsWordCloud(Highcharts);

// Types for specialized charts
export interface WordCloudData {
  name: string;
  weight: number;
}

export interface NetworkGraphData {
  nodes: Array<{
    id: string;
    name: string;
    color?: string;
  }>;
  links: Array<{
    from: string;
    to: string;
    weight?: number;
  }>;
}

export interface MapData {
  key: string;
  value: number;
  name?: string;
}

/**
 * نمایش ابری کلمات (Word Cloud)
 */
export const WordCloudChart: React.FC<{
  data?: WordCloudData[];
  title?: string;
  className?: string;
  showLegend?: boolean;
}> = memo(
  ({
    data = [
      { name: 'تلگرام', weight: 100 },
      { name: 'اینستاگرام', weight: 80 },
      { name: 'توییتر', weight: 60 },
      { name: 'یوتوب', weight: 50 },
      { name: 'لینکدین', weight: 40 },
      { name: 'فیسبوک', weight: 35 },
      { name: 'تیک‌تاک', weight: 30 },
      { name: 'اسنپ‌چت', weight: 25 },
      { name: 'پینترست', weight: 20 },
      { name: 'ردیت', weight: 15 },
    ],
    title = 'ابر کلمات',
    className = '',
    showLegend = false,
  }) => {
    const options = createChartConfig(
      {
        chart: {
          type: 'wordcloud',
        },
        title: {
          text: title,
        },
        plotOptions: {
          wordcloud: {
            allowExtendPlayingField: true,
            animation: false,
            cursor: 'pointer',
            dataLabels: {
              enabled: false,
            },
            fontFamily: 'IranYekanX',
            maxFontSize: 50,
            minFontSize: 10,
            placementStrategy: 'random',
            rotation: {
              from: -45,
              to: 45,
              orientations: 5,
            },
            spiral: 'rectangular',
            style: {
              fontWeight: 'bold',
              fontFamily: 'IranYekanX',
              textOutline: '1px black',
            },
          },
        },
        series: [
          {
            type: 'wordcloud',
            name: 'وزن',
            data: data,
          },
        ],
        responsive: RESPONSIVE_RULES,
      },
      showLegend
    );

    return (
      <ResponsiveChartWrapper
        options={options}
        className={className}
        minHeight={300}
      />
    );
  }
);

/**
 * گراف شبکه (Network Graph)
 */
export const NetworkGraph: React.FC<{
  data?: NetworkGraphData;
  title?: string;
  className?: string;
}> = React.memo(
  ({
    data = {
      nodes: [
        { id: 'telegram', name: 'تلگرام', color: '#004748' },
        { id: 'instagram', name: 'اینستاگرام', color: '#026B6E' },
        { id: 'twitter', name: 'توییتر', color: '#048F92' },
        { id: 'youtube', name: 'یوتوب', color: '#5BF7FA' },
        { id: 'linkedin', name: 'لینکدین', color: '#9EFEFF' },
      ],
      links: [
        { from: 'telegram', to: 'instagram', weight: 5 },
        { from: 'telegram', to: 'twitter', weight: 3 },
        { from: 'instagram', to: 'youtube', weight: 4 },
        { from: 'twitter', to: 'linkedin', weight: 2 },
        { from: 'youtube', to: 'linkedin', weight: 3 },
      ],
    },
    title = 'گراف شبکه',
    className = '',
  }) => {
    const graphRef = useRef<ForceGraphMethods | undefined>();
    const resizeObserverRef = useRef<ResizeObserver>();

    const [width, setWidth] = React.useState(100);
    const [height, setHeight] = React.useState(100);

    // Use the responsive hook for automatic resizing
    const { containerRef } = useForceGraphResize(graphRef, {
      minHeight: 400,
      debounceDelay: 100,
      autoFitOnResize: true,
    });

    useEffect(() => {
      const container = containerRef.current;
      if (!container) return;

      // Create ResizeObserver to watch container size changes
      resizeObserverRef.current = new ResizeObserver((entries) => {
        for (const entry of entries) {
          if (entry.target === container) {
            setWidth(container?.getBoundingClientRect()?.width || 0);
            setHeight(container?.getBoundingClientRect()?.height || 0);
          }
        }
      });

      // Start observing the container
      resizeObserverRef.current.observe(container);

      // Initial resize
      setTimeout(() => {
        setWidth(container?.getBoundingClientRect()?.width || 0);
        setHeight(container?.getBoundingClientRect()?.height || 0);
      }, 50);

      // Cleanup
      return () => {
        if (resizeObserverRef.current) {
          resizeObserverRef.current.disconnect();
        }
      };
    }, []);

    // Transform data for react-force-graph-2d
    const graphData = {
      nodes: data.nodes.map((node) => ({
        id: node.id,
        name: node.name,
        color: node.color || '#5BF7FA',
      })),
      links: data.links.map((link) => ({
        source: link.from,
        target: link.to,
        value: link.weight || 1,
      })),
    };

    useEffect(() => {
      if (graphRef.current) {
        // Auto-fit graph to container on data change
        graphRef.current.zoomToFit(10);
      }
    }, [data]);

    return (
      <div
        ref={containerRef}
        className={`h-full w-full rounded-[5px] border border-[#323538] bg-[#181A1B] p-2 ${className}`}
      >
        {title && (
          <div className="p-3 text-center font-bold text-white">{title}</div>
        )}
        <div
          className={title ? 'h-[calc(100%-60px)]' : 'h-full'}
          style={{ minHeight: '400px', width: '100%', height: '100%' }}
        >
          <ForceGraph2D
            ref={graphRef}
            graphData={graphData}
            width={width - 40}
            height={height - 90}
            backgroundColor="transparent"
            nodeLabel="name"
            nodeColor={(node) => node.color}
            nodeRelSize={3}
            nodeCanvasObject={(node, ctx, globalScale) => {
              const label = node.name || '';
              const fontSize = 12 / globalScale;
              const x = node.x || 0;
              const y = node.y || 0;

              ctx.font = `${fontSize}px IranYekanX, Arial, sans-serif`;
              ctx.textAlign = 'center';
              ctx.textBaseline = 'middle';
              ctx.fillStyle = node.color || '#5BF7FA';

              // Draw node circle
              ctx.beginPath();
              ctx.arc(x, y, 4, 0, 2 * Math.PI, false);
              ctx.fill();

              // Draw label
              ctx.fillStyle = '#ffffff';
              ctx.fillText(label, x, y + 20);
            }}
            linkColor={() => '#5BF7FA'}
            linkWidth={(link) => Math.sqrt(link.value) * 1}
            linkDirectionalParticles={2}
            linkDirectionalParticleSpeed={0.006}
            linkDirectionalParticleWidth={2}
            linkDirectionalParticleColor={() => '#9EFEFF'}
            d3AlphaDecay={0.02}
            d3VelocityDecay={0.3}
            cooldownTicks={100}
            onEngineStop={() => graphRef.current?.zoomToFit(10)}
          />
        </div>
      </div>
    );
  }
);

/**
 * نقشه ایران
 */
export const IranMapChart: React.FC<{
  data?: MapData[];
  title?: string;
  className?: string;
  showLegend?: boolean;
}> = ({
  data = [
    { key: 'ir-5428', value: 85, name: 'تهران' },
    { key: 'ir-3423', value: 72, name: 'اصفهان' },
    { key: 'ir-3427', value: 68, name: 'فارس' },
    { key: 'ir-3426', value: 59, name: 'خراسان رضوی' },
    { key: 'ir-3424', value: 45, name: 'آذربایجان شرقی' },
  ],
  title = 'نقشه ایران',
  className = '',
  showLegend = false,
}) => {
  const mapData =
    data ||
    iranMap.features.map((f: any) => ({
      key: f.properties['hc-key'],
      value: Math.floor(Math.random() * 100),
    }));

  const options = createChartConfig(
    {
      chart: {
        map: iranMap as any,
        backgroundColor: 'transparent',
        zoomType: undefined,
        panning: false,
        // Remove fixed dimensions to allow dynamic resizing
        animation: false,
      },
      mapNavigation: {
        enabled: false,
        enableButtons: false,
        enableMouseWheelZoom: false,
        enableTouchZoom: false,
        enableDoubleClickZoom: false,
        buttonOptions: {
          enabled: false,
          verticalAlign: 'bottom',
        },
      },
      title: {
        text: title || undefined,
        style: {
          color: '#ffffff',
          fontFamily: 'iranyekanx',
          fontSize: '14px',
        },
      },
      legend: {
        enabled: false,
      },
      credits: {
        enabled: false,
      },
      colorAxis: {
        min: 0,
        max: 100,
        stops: [
          [0, '#CCF4F5'],
          [0.5, '#05A0A3'],
          [1, '#026B6E'],
        ],
        labels: {
          style: {
            color: '#ffffff',
            fontSize: '10px',
          },
        },
      },
      tooltip: {
        backgroundColor: '#1f2937',
        borderColor: '#374151',
        style: {
          color: '#ffffff',
          fontSize: '10px',
        },
      },
      series: [
        {
          type: 'map',
          data: mapData,
          name: 'Value',
          states: {
            hover: {
              color: '#BADA55',
            },
          },
          dataLabels: {
            enabled: false,
          },
          animation: false,
        },
      ],
      responsive: {
        rules: [
          {
            condition: {
              maxWidth: 300,
            },
            chartOptions: {
              title: {
                style: {
                  fontSize: '12px',
                },
              },
              colorAxis: {
                labels: {
                  style: {
                    fontSize: '8px',
                  },
                },
              },
            },
          },
        ],
      },
    },
    showLegend
  );

  return (
    <ResponsiveChartWrapper
      options={options}
      className={className}
      minHeight={300}
    />
  );
};

/**
 * نقشه جهان
 */
export const WorldMapChart: React.FC<{
  data?: Array<{
    code: string;
    value: number;
    name?: string;
  }>;
  title?: string;
  className?: string;
  showLegend?: boolean;
}> = ({
  data = [
    { code: 'IR', value: 100, name: 'ایران' },
    { code: 'US', value: 85, name: 'آمریکا' },
    { code: 'DE', value: 70, name: 'آلمان' },
    { code: 'FR', value: 65, name: 'فرانسه' },
    { code: 'GB', value: 60, name: 'انگلستان' },
    { code: 'JP', value: 55, name: 'ژاپن' },
    { code: 'CN', value: 90, name: 'چین' },
  ],
  title = 'نقشه جهان',
  className = '',
  showLegend = false,
}) => {
  const options = createChartConfig(
    {
      chart: {
        type: 'map',
        map: worldMap as any,
      },
      title: {
        text: title,
      },
      mapNavigation: {
        enabled: true,
        buttonOptions: {
          verticalAlign: 'bottom',
        },
      },
      colorAxis: {
        min: 0,
        minColor: '#004748',
        maxColor: '#5BF7FA',
        labels: {
          style: {
            color: '#ffffff',
            fontFamily: 'IranYekanX',
          },
        },
      },
      plotOptions: {
        map: {
          dataLabels: {
            enabled: false,
          },
          tooltip: {
            pointFormat: '{point.name}: <b>{point.value}</b>',
          },
        },
      },
      series: [
        {
          type: 'map',
          name: 'مقدار',
          data: data.map((item) => ({
            'hc-key': item.code.toLowerCase(),
            value: item.value,
            name: item.name,
          })),
        },
      ],
      responsive: RESPONSIVE_RULES,
    },
    showLegend
  );

  return (
    <ResponsiveChartWrapper
      options={options}
      className={className}
      minHeight={400}
    />
  );
};
