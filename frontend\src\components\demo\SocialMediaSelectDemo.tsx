import React, { useState } from 'react';
import SocialMediaSelect from '@/components/ui/SocialMediaSelect';
import { socialItems } from '@/constants/socialMedia';

const SocialMediaSelectDemo: React.FC = () => {
  const [selectedSocials, setSelectedSocials] = useState<number[]>([1]);
  const [error, setError] = useState<string>('');

  const handleSelectionChange = (selectedIds: number[]) => {
    setSelectedSocials(selectedIds);
    setError(''); // Clear error when selection changes
  };

  const handleValidate = () => {
    if (selectedSocials.length === 0) {
      setError('حداقل یک بستر باید انتخاب شود.');
    } else {
      setError('');
      alert(`انتخاب شده: ${selectedSocials.map(id => 
        socialItems.find(item => item.id === id)?.title
      ).join(', ')}`);
    }
  };

  return (
    <div className="p-8 bg-neutral-900 min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8 text-center">
          نمایش کامپوننت انتخاب شبکه‌های اجتماعی
        </h1>
        
        <SocialMediaSelect
          label="بستر‌های مورد نظر خود را انتخاب کنید"
          value={selectedSocials}
          onChange={handleSelectionChange}
          error={error}
          className="mb-6"
        />
        
        <div className="text-center">
          <button
            onClick={handleValidate}
            className="px-6 py-3 bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors"
          >
            تایید انتخاب
          </button>
        </div>
        
        <div className="mt-6 p-4 bg-neutral-800 rounded-lg">
          <h3 className="text-lg font-medium text-white mb-2">انتخاب فعلی:</h3>
          <p className="text-neutral-300">
            {selectedSocials.length > 0 
              ? selectedSocials.map(id => 
                  socialItems.find(item => item.id === id)?.title
                ).join(', ')
              : 'هیچ بستری انتخاب نشده'
            }
          </p>
          <p className="text-sm text-neutral-400 mt-2">
            تعداد انتخاب شده: {selectedSocials.length}
          </p>
        </div>
      </div>
    </div>
  );
};

export default SocialMediaSelectDemo;
