import React from 'react';
import { cn } from '@/utils/utlis';
import { socialItems, SocialItem } from '@/constants/socialMedia';

interface SocialMediaSelectProps {
  label?: string;
  value?: number[];
  onChange?: (selectedIds: number[]) => void;
  error?: string;
  className?: string;
  minSelection?: number;
}

const SocialMediaSelect: React.FC<SocialMediaSelectProps> = ({
  label,
  value = [1], // Default to Telegram selected
  onChange,
  error,
  className,
  minSelection = 1,
}) => {
  const handleItemClick = (itemId: number) => {
    if (!onChange) return;

    // Prevent deselecting if it would go below minimum selection
    if (value.length === minSelection && value.includes(itemId)) {
      return;
    }

    const newSelection = value.includes(itemId)
      ? value.filter((id) => id !== itemId)
      : [...value, itemId];

    onChange(newSelection);
  };

  return (
    <div className={cn('w-full', className)}>
      {label && (
        <label className="mb-2 block font-medium text-neutral-300">
          {label}
        </label>
      )}
      
      <div className="flex w-full flex-wrap justify-center gap-5">
        {socialItems.map((item: SocialItem) => {
          const Icon = item.icon;
          const isSelected = value.includes(item.id);
          
          return (
            <div
              key={item.id}
              className={cn(
                'flex h-[175px] w-[175px] shrink-0 cursor-pointer flex-col items-center justify-between rounded-lg border-2 bg-neutral-900 p-6 text-center shadow-lg transition-colors select-none sm:w-[calc(50%-0.625rem)] md:w-[175px]',
                isSelected
                  ? 'border-primary-400'
                  : 'border-neutral-700',
                error && 'border-red-500'
              )}
              onClick={() => handleItemClick(item.id)}
            >
              <Icon size={46} className="fill-primary-400" />
              <div className="text-lg font-bold text-white">{item.title}</div>
              <div className="text-sm font-bold text-neutral-500">
                {item.description}
              </div>
            </div>
          );
        })}
      </div>
      
      {error && <p className="mt-2 text-xs text-red-500">{error}</p>}
    </div>
  );
};

export default SocialMediaSelect;
