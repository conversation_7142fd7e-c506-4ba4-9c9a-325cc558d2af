// src/api/auth.ts
import apiClient from './apiClient';

export interface LoginCredentials {
  credit: string;
  password: string;
}

export interface AuthResponse {
  access: string;
  refresh: string;
}

export interface User {
  uuid: string;
  last_login: string;
  avatar: string;
  username: string;
  first_name: string;
  last_name: string;
  email: string;
  phone_number: string;
  registration_date: string;
  role: string;
  groups: string[];
  user_permissions: string[];
}

// Authentication API calls
export const login = async (
  credit: string,
  password: string
): Promise<AuthResponse> => {
  try {
    const response = await apiClient.post<AuthResponse>('/account/login/', {
      credit,
      password,
    });
    return response.data;
  } catch (error: unknown) {
    console.error('Login error:', error);

    // Handle different types of errors
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response: { status: number } };
      if (axiosError.response?.status === 401) {
        throw new Error('نام کاربری یا کلمه عبور اشتباه است');
      } else if (axiosError.response?.status === 400) {
        throw new Error('اطلاعات وارد شده نامعتبر است');
      } else if (axiosError.response?.status === 500) {
        throw new Error('خطا در سرور. لطفا بعدا تلاش کنید');
      }
    }

    if (error && typeof error === 'object' && 'code' in error) {
      const networkError = error as { code: string };
      if (networkError.code === 'NETWORK_ERROR') {
        throw new Error('خطا در اتصال به سرور');
      }
    }

    throw new Error('خطا در ورود به سامانه');
  }
};

interface GetMeResponse {
  status: string;
  code: number;
  message: string;
  data: User;
}

export const getMe = async (): Promise<User> => {
  try {
    const response = await apiClient.get<GetMeResponse>('/account/get_me/');
    if (!response.data.data) {
      throw new Error('Invalid response format');
    }
    return response.data.data;
  } catch (error) {
    console.error('Get me error:', error);
    throw new Error('Failed to get user info');
  }
};

export const refreshAuthToken = async (
  refreshToken: string
): Promise<string> => {
  try {
    const response = await apiClient.post<{ access: string }>(
      '/account/login/refresh/',
      { refresh: refreshToken }
    );
    return response.data.access;
  } catch (error) {
    console.error('Refresh token error:', error);
    throw new Error('Failed to refresh token');
  }
};

export const logoutUser = async (): Promise<void> => {
  try {
    await apiClient.post('/account/logout/');
  } catch (error) {
    // Even if logout fails on server, we'll clear local storage
    console.error('Logout error:', error);
  }
};
