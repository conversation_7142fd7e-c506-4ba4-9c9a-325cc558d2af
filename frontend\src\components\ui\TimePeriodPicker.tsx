import React, { useEffect, useState, useRef } from 'react';
import { Clock, Calendar, Info } from 'lucide-react';
import clsx from 'clsx';
import styles from './TimePeriodPicker.module.css';

type PeriodUnit = 'hour' | 'day';

interface Props {
  value: number | undefined;
  onChange: (value: number) => void;
  label?: string;
}

const presets: Record<PeriodUnit, number[]> = {
  hour: [1, 6, 12, 24, 48, 72, 186, 720],
  day: [1, 3, 7, 14, 30],
};

const maxValues: Record<PeriodUnit, number> = {
  hour: 720,
  day: 30,
};

const TimePeriodPicker: React.FC<Props> = ({ value, onChange, label = '' }) => {
  const [unit, setUnit] = useState<PeriodUnit>('hour');
  const [amount, setAmount] = useState<number>(12);
  const isInternalUpdate = useRef(false);

  useEffect(() => {
    if (value && !isInternalUpdate.current) {
      const hours = value / (60 * 60 * 1000);
      const days = hours / 24;

      if (hours <= 720 && hours % 1 === 0) {
        setUnit('hour');
        setAmount(Math.round(hours));
      } else if (days <= 30) {
        setUnit('day');
        setAmount(Math.round(days));
      } else {
        setUnit('hour');
        setAmount(Math.min(720, Math.round(hours)));
      }
    }
    isInternalUpdate.current = false;
  }, [value]);

  useEffect(() => {
    const timestamp =
      unit === 'hour' ? amount * 60 * 60 * 1000 : amount * 24 * 60 * 60 * 1000;

    // Only call onChange if the calculated timestamp is different from the current value
    if (timestamp !== value) {
      isInternalUpdate.current = true;
      onChange(timestamp);
    }
  }, [unit, amount, value, onChange]);

  const handleUnitChange = (newUnit: PeriodUnit) => {
    isInternalUpdate.current = true;
    setUnit(newUnit);
    setAmount(presets[newUnit][2] || presets[newUnit][0]);
  };

  const toggleUnit = () => {
    handleUnitChange(unit === 'hour' ? 'day' : 'hour');
  };

  const formatPeriodText = (value: number, unit: PeriodUnit) => {
    const unitText = unit === 'hour' ? 'ساعت' : 'روز';
    return `${value} ${unitText}`;
  };

  const getSliderPercentage = () => (amount / maxValues[unit]) * 100;

  const handleNumber = (e: React.ChangeEvent<HTMLInputElement>) => {
    const val = parseInt(e.target.value, 10);

    isInternalUpdate.current = true;
    if (!isNaN(val)) {
      const min = 1;
      const max = maxValues[unit];
      const clamped = Math.min(Math.max(val, min), max);
      setAmount(clamped);
    } else {
      setAmount(1); // Set to min if input is invalid or empty
    }
  };


  return (
    <div className="font-iran-yekan-x-fanum">
      <div className="mb-2 block font-medium text-neutral-300">{label}</div>

      <div className="rounded-md bg-[#3b3b3b] p-6" dir="rtl">
        <div className="mb-6 flex items-center justify-between">
          <div className="flex items-center gap-2 text-sm text-gray-400">
            <span>واحد زمان</span>
            <Info className="text-primary-400 h-4 w-4" />
          </div>
        </div>

        <div
          className={clsx(
            'relative flex h-14 w-72 cursor-pointer items-center rounded-lg border border-[#727272] p-1',
            styles.toggle
          )}
          onClick={toggleUnit}
        >
          <div
            className={clsx(
              'absolute h-12 w-1/2 rounded-lg transition-all duration-300 ease-out',
              unit === 'hour'
                ? 'bg-primary-500 right-1'
                : 'bg-primary-500 left-1'
            )}
          />
          <div className="relative z-10 flex w-full justify-between px-2 text-base font-semibold">
            <button
              type="button"
              className={clsx(
                'pointer-events-none z-10 flex h-full w-full items-center justify-center rounded-lg transition-colors duration-300',
                unit === 'hour' ? 'text-white' : 'text-gray-400'
              )}
            >
              <Clock className="ml-2 h-4 w-4" />
              ساعت
            </button>
            <button
              type="button"
              className={clsx(
                'pointer-events-none z-10 flex h-full w-full items-center justify-center rounded-lg transition-colors duration-300',
                unit === 'day' ? 'text-white' : 'text-gray-400'
              )}
            >
              <Calendar className="ml-2 h-4 w-4" />
              روز
            </button>
          </div>
        </div>

        <div className="mt-6 flex items-center gap-1.5">
          <span className="text-sm text-[#CFCFCF]">مقدار بازه</span>
          <span className="text-sm text-[#A89F91]">
            {unit === 'hour' ? '(۱ تا ۷۲۰ ساعت)' : '(۱ تا 30 روز)'}
          </span>
          <Info className="text-primary-400 h-4 w-4" />
        </div>

        <div className="mt-4 mb-6 flex flex-wrap gap-3">
          {presets[unit].map((val) => (
            <button
              key={val}
              onClick={() => {
                isInternalUpdate.current = true;
                setAmount(val);
              }}
              className={clsx(
                'cursor-pointer rounded-lg border-1 px-5 py-3 text-sm font-medium transition-all duration-200',
                amount === val
                  ? 'scale-105 border-[#00aab2] bg-gradient-to-r from-[#00aab2] to-[#00c4cc] text-white shadow-lg'
                  : 'border-[#727272] text-gray-300 hover:border-[#4a4a4a]'
              )}
            >
              {formatPeriodText(val, unit)}
            </button>
          ))}
        </div>

        <div className="relative mt-12 flex items-center gap-4">
          <input
            type="range"
            min={1}
            max={maxValues[unit]}
            step={1}
            value={amount}
            onChange={(e) => {
              isInternalUpdate.current = true;
              setAmount(Number(e.target.value));
            }}
            className={clsx(styles.slider)}
            style={{
              background: `linear-gradient(to left, #00aab2 0%, #00aab2 ${getSliderPercentage()}%, #1a1a1a ${getSliderPercentage()}%, #1a1a1a 100%)`,
            }}
          />
          <input
            type="number"
            value={amount}
            min={1}
            max={maxValues[unit]}
            onChange={handleNumber}
            className="min-w-20 input-no-spinner focus:border-primary-400 focus:ring-primary-400 focus:outline-none rounded-lg border-1 border-[#727272] px-5 py-3 text-center text-sm font-medium text-gray-300 transition-all duration-200"
          />
        </div>
      </div>
    </div>
  );
};

export default TimePeriodPicker;
