import { useEffect } from 'react';
import { Mail, Phone, Globe, MapPin, Calendar, Users, FileText, Eye, Heart } from 'lucide-react';
import { cn } from '@/utils/utlis';
import { User } from './UserCard';

interface UserDetailDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  user: User | null;
  onSelect?: (user: User) => void;
  isSelected?: boolean;
}

export default function UserDetailDrawer({
  isOpen,
  onClose,
  user,
  onSelect,
  isSelected = false,
}: UserDetailDrawerProps) {
  // Close drawer on Escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  const handleSelect = () => {
    if (user && onSelect) {
      onSelect(user);
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'نامشخص';
    return new Date(dateString).toLocaleDateString('fa-IR');
  };

  const formatLastSeen = (lastSeen?: string) => {
    if (!lastSeen) return 'نامشخص';
    const date = new Date(lastSeen);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'همین الان';
    if (diffInMinutes < 60) return `${diffInMinutes} دقیقه پیش`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} ساعت پیش`;
    return `${Math.floor(diffInMinutes / 1440)} روز پیش`;
  };

  if (!user) return null;

  return (
    <div className="h-full flex flex-col overflow-hidden">
      {isOpen && user && (
        <div className="h-full flex flex-col overflow-y-auto">
          {/* Futuristic border glow effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-primary-400/5 via-transparent to-transparent opacity-50" />

          {/* Header */}
          <div className="relative border-b border-primary-400/30 bg-gradient-to-r from-gray-800/50 to-gray-900/50 p-4">
            <div className="flex items-center justify-center">
              <h3 className="text-lg font-bold text-white">جزئیات منبع</h3>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 p-4 space-y-4 overflow-y-auto">
            {/* Avatar and Basic Info */}
            <div className="text-center">
              <div className="relative mx-auto mb-3 w-20 h-20">
                  <div
                    className={cn(
                      'w-full h-full rounded-full border-3 bg-gradient-to-br from-gray-700 to-gray-800 p-1',
                      isSelected
                        ? 'border-primary-400 shadow-lg shadow-primary-400/30'
                        : 'border-gray-600'
                    )}
                  >
                    <img
                      src={user.avatar}
                      alt={user.name}
                      className="w-full h-full rounded-full object-cover"
                    />
                  </div>
                  
                  {/* Online status indicator */}
                  {user.isOnline && (
                    <div className="absolute -bottom-1 -right-1 h-6 w-6 rounded-full border-3 border-gray-900 bg-green-400"></div>
                  )}
                </div>

              <h2 className="text-lg font-bold text-white mb-1">{user.name}</h2>
              <p className="text-primary-400 mb-2 text-sm">@{user.username}</p>
                
              {/* Online Status */}
              <div className="flex items-center justify-center gap-2 text-xs">
                <div className={cn(
                  "w-2 h-2 rounded-full",
                  user.isOnline ? "bg-green-400" : "bg-gray-500"
                )} />
                <span className="text-gray-400">
                  {user.isOnline ? 'آنلاین' : `آخرین بازدید: ${formatLastSeen(user.lastSeen)}`}
                </span>
              </div>
            </div>

            {/* Bio */}
            {user.bio && (
              <div className="p-3 rounded-lg border border-gray-700/50 bg-gray-800/30">
                <h4 className="text-xs font-semibold text-primary-400 mb-2">درباره</h4>
                <p className="text-gray-300 text-xs leading-relaxed">{user.bio}</p>
              </div>
            )}

            {/* Contact Information */}
            <div className="space-y-2">
              <h4 className="text-xs font-semibold text-primary-400">اطلاعات تماس</h4>
                
              {user.email && (
                <div className="flex items-center gap-2 text-xs">
                  <Mail size={12} className="text-gray-400" />
                  <span className="text-gray-300 truncate">{user.email}</span>
                </div>
              )}

              {user.phone && (
                <div className="flex items-center gap-2 text-xs">
                  <Phone size={12} className="text-gray-400" />
                  <span className="text-gray-300">{user.phone}</span>
                </div>
              )}

              {user.website && (
                <div className="flex items-center gap-2 text-xs">
                  <Globe size={12} className="text-gray-400" />
                  <a href={user.website} target="_blank" rel="noopener noreferrer" className="text-primary-400 hover:text-primary-300 truncate">
                    {user.website}
                  </a>
                </div>
              )}

              {user.location && (
                <div className="flex items-center gap-2 text-xs">
                  <MapPin size={12} className="text-gray-400" />
                  <span className="text-gray-300">{user.location}</span>
                </div>
              )}

              {user.joinDate && (
                <div className="flex items-center gap-2 text-xs">
                  <Calendar size={12} className="text-gray-400" />
                  <span className="text-gray-300">عضویت از {formatDate(user.joinDate)}</span>
                </div>
              )}
            </div>

            {/* Statistics */}
            {user.stats && (
              <div className="space-y-2">
                <h4 className="text-xs font-semibold text-primary-400">آمار</h4>
                <div className="grid grid-cols-2 gap-2">
                  {user.stats.posts !== undefined && (
                    <div className="p-2 rounded-lg border border-gray-700/50 bg-gray-800/30 text-center">
                      <div className="flex items-center justify-center gap-1 mb-1">
                        <FileText size={10} className="text-gray-400" />
                      </div>
                      <div className="text-sm font-bold text-white">{user.stats.posts.toLocaleString()}</div>
                      <div className="text-xs text-gray-400">پست</div>
                    </div>
                  )}

                  {user.stats.followers !== undefined && (
                    <div className="p-2 rounded-lg border border-gray-700/50 bg-gray-800/30 text-center">
                      <div className="flex items-center justify-center gap-1 mb-1">
                        <Users size={10} className="text-gray-400" />
                      </div>
                      <div className="text-sm font-bold text-white">{user.stats.followers.toLocaleString()}</div>
                      <div className="text-xs text-gray-400">دنبال‌کننده</div>
                    </div>
                  )}

                  {user.stats.following !== undefined && (
                    <div className="p-2 rounded-lg border border-gray-700/50 bg-gray-800/30 text-center">
                      <div className="flex items-center justify-center gap-1 mb-1">
                        <Heart size={10} className="text-gray-400" />
                      </div>
                      <div className="text-sm font-bold text-white">{user.stats.following.toLocaleString()}</div>
                      <div className="text-xs text-gray-400">دنبال شده</div>
                    </div>
                  )}

                  {user.stats.articles !== undefined && (
                    <div className="p-2 rounded-lg border border-gray-700/50 bg-gray-800/30 text-center">
                      <div className="flex items-center justify-center gap-1 mb-1">
                        <Eye size={10} className="text-gray-400" />
                      </div>
                      <div className="text-sm font-bold text-white">{user.stats.articles.toLocaleString()}</div>
                      <div className="text-xs text-gray-400">مقاله</div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Action Button */}
            {onSelect && (
              <button
                onClick={handleSelect}
                className={cn(
                  "w-full rounded-lg px-3 py-2 text-sm font-medium transition-all duration-200",
                  isSelected
                    ? "bg-red-500 text-white hover:bg-red-600"
                    : "bg-primary-500 text-white hover:bg-primary-600"
                )}
              >
                {isSelected ? 'حذف از انتخاب‌ها' : 'افزودن به انتخاب‌ها'}
              </button>
            )}
          </div>
        </div>
      )}

      {/* Show placeholder when no user is selected */}
      {!user && (
        <div className="h-full flex items-center justify-center p-4">
          <div className="text-center">
            <Users size={48} className="mx-auto mb-4 text-gray-600" />
            <p className="text-gray-400 text-sm">هیچ منبعی انتخاب نشده</p>
            <p className="text-gray-500 text-xs mt-2">روی یکی از کارت‌ها کلیک کنید</p>
          </div>
        </div>
      )}
    </div>
  );
}
