import { useState } from 'react';
import TagInput from '@/components/ui/TagInput';
import { User } from '@/components/ui/UserCard';

// Simple test data
const testUsers: User[] = [
  {
    id: '1',
    name: 'تست کاربر ۱',
    username: 'test1',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    isOnline: true,
    bio: 'این یک کاربر تست است',
    email: '<EMAIL>',
    stats: { posts: 100, followers: 500, following: 200, articles: 50 },
  },
  {
    id: '2',
    name: 'تست کاربر ۲',
    username: 'test2',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    isOnline: false,
    bio: 'این یک کاربر تست دیگر است',
    email: '<EMAIL>',
    stats: { posts: 200, followers: 800, following: 300, articles: 75 },
  },
  {
    id: '3',
    name: 'تست کاربر ۳',
    username: 'test3',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    isOnline: true,
    bio: 'سومین کاربر تست',
    email: '<EMAIL>',
    stats: { posts: 150, followers: 600, following: 250, articles: 60 },
  },
];

export default function TestSourceInput() {
  const [selectedTags, setSelectedTags] = useState<string[]>([]);

  const handleTagsChange = (newTags: string[]) => {
    console.log('Tags changed to:', newTags);
    setSelectedTags(newTags);
  };

  return (
    <div className="min-h-screen bg-black p-8" dir="rtl">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8 text-center">
          تست SourceInput
        </h1>

        <div className="space-y-6">
          <div className="p-6 rounded-lg border border-primary-400/30 bg-gradient-to-br from-gray-900/70 to-black/70">
            <h2 className="text-xl font-semibold text-primary-400 mb-4">
              تست انتخاب منابع
            </h2>
            
            <TagInput
              label="انتخاب منابع تست"
              value={selectedTags}
              onChange={handleTagsChange}
              inputMode="select"
              users={testUsers}
              showMoreEnabled={true}
              modalTitle="انتخاب منابع تست"
              searchPlaceholder="جستجو در منابع تست..."
              placeholder="منابع تست را انتخاب کنید..."
              mode="wrap"
            />
            
            <div className="mt-4 p-4 bg-gray-800/50 rounded-lg">
              <h3 className="text-white font-semibold mb-2">وضعیت فعلی:</h3>
              <p className="text-gray-300 text-sm">
                تعداد تگ‌های انتخاب شده: {selectedTags.length}
              </p>
              <p className="text-gray-300 text-sm">
                تگ‌ها: {selectedTags.join(', ') || 'هیچ کدام'}
              </p>
            </div>
          </div>

          <div className="p-4 rounded-lg border border-blue-500/30 bg-blue-900/10">
            <h3 className="text-lg font-semibold text-blue-400 mb-2">راهنمای تست:</h3>
            <ul className="space-y-1 text-gray-300 text-sm">
              <li>• روی "نمایش بیشتر..." کلیک کنید تا مودال باز شود</li>
              <li>• روی دکمه کوچک در گوشه کارت‌ها کلیک کنید تا انتخاب کنید</li>
              <li>• روی کارت‌ها کلیک کنید تا جزئیات را ببینید</li>
              <li>• روی "تأیید" کلیک کنید تا تغییرات اعمال شود</li>
              <li>• کنسول مرورگر را باز کنید تا لاگ‌ها را ببینید</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
