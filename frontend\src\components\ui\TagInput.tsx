import { KeyboardEvent, useEffect, useRef, useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import TextInput from '@/components/ui/TextInput';
import { X, ChevronDown, MoreHorizontal } from 'lucide-react';
import { cn } from '@/utils/utlis';
import SourceInput from './SourceInput';
import { User } from './UserCard';

export interface SelectOption {
  value: string;
  label: string;
}

interface TagInputProps {
  label?: string;
  value?: string[];
  onChange?: (tags: string[]) => void;
  initialTags?: string[];
  mode?: 'wrap' | 'scroll';
  inputMode?: 'text' | 'select';
  options?: SelectOption[];
  placeholder?: string;
  // Additional props for text input mode
  disabled?: boolean;
  className?: string;
  name?: string;
  // Props for modal functionality
  users?: User[];
  showMoreEnabled?: boolean;
  modalTitle?: string;
  searchPlaceholder?: string;
}

export default function TagInput({
  label,
  value,
  onChange,
  initialTags = [],
  mode = 'wrap',
  inputMode = 'text',
  options = [],
  placeholder = 'انتخاب کنید',
  disabled,
  className,
  name,
  users = [],
  showMoreEnabled = false,
  modalTitle = 'انتخاب کاربران',
  searchPlaceholder = 'جستجو کاربران...'
}: TagInputProps) {
  const [internalTags, setInternalTags] = useState<string[]>(initialTags);
  const [input, setInput] = useState('');
  const [isSelectOpen, setIsSelectOpen] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<User[]>(() => {
    // Initialize selectedUsers based on existing tags and users
    const currentTags = value !== undefined ? value : initialTags;
    return users.filter(user => currentTags.includes(user.username));
  });
  const containerRef = useRef<HTMLDivElement>(null);
  const selectRef = useRef<HTMLDivElement>(null);
  const isDragging = useRef(false);
  const startX = useRef(0);
  const scrollLeft = useRef(0);

  const tags = value !== undefined ? value : internalTags;

  // Keep selectedUsers in sync with tags
  useEffect(() => {
    const currentSelectedUsers = users.filter(user => tags.includes(user.username));
    setSelectedUsers(currentSelectedUsers);
  }, [tags, users]);

  const updateTags = (newTags: string[]) => {
    if (onChange) {
      onChange(newTags);
    } else {
      setInternalTags(newTags);
    }
  };

  const addTag = (tag: string) => {
    const trimmed = tag.trim();
    if (trimmed && !tags.includes(trimmed)) {
      updateTags([...tags, trimmed]);
    }
    setInput('');
  };

  const removeTag = (tagToRemove: string) => {
    updateTags(tags.filter((tag) => tag !== tagToRemove));
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' || e.key === ',' || e.key === ' ') {
      e.preventDefault();
      if (input.trim()) addTag(input);
    } else if (e.key === 'Backspace' && !input && tags.length) {
      removeTag(tags[tags.length - 1]);
    }
  };

  const handleSelectOption = (optionValue: string) => {
    addTag(optionValue);
    setIsSelectOpen(false);
  };

  const handleShowMore = () => {
    setIsSelectOpen(false);
    setIsModalOpen(true);
  };

  const handleModalSelect = (users: User[]) => {
    setSelectedUsers(users);
    // Convert users to tags
    const userTags = users.map(user => user.username);
    // Get all existing tags that are not from users (like manual tags or options)
    const allUsernames = [...users.map(u => u.username), ...selectedUsers.map(u => u.username)];
    const nonUserTags = tags.filter(tag => !allUsernames.includes(tag));
    // Combine non-user tags with new user tags
    const finalTags = [...nonUserTags, ...userTags];
    updateTags(finalTags);
  };

  // Get available options (excluding already selected tags)
  const availableOptions = options.filter(option => !tags.includes(option.value));

  // Handle click outside for select dropdown
  useEffect(() => {
    if (inputMode !== 'select') return;

    const handleClickOutside = (event: MouseEvent) => {
      if (
        selectRef.current &&
        !selectRef.current.contains(event.target as Node)
      ) {
        setIsSelectOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [inputMode]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container || mode !== 'scroll') return;

    const handleMouseDown = (e: MouseEvent | TouchEvent) => {
      isDragging.current = true;
      startX.current = 'touches' in e ? e.touches[0].clientX : e.clientX;
      scrollLeft.current = container.scrollLeft;
    };

    const handleMouseMove = (e: MouseEvent | TouchEvent) => {
      if (!isDragging.current) return;
      const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;
      const walk = (clientX - startX.current) * -1; // Reverse for RTL
      container.scrollLeft = scrollLeft.current + walk;
    };

    const stopDrag = () => {
      isDragging.current = false;
    };

    // Mouse events
    container.addEventListener('mousedown', handleMouseDown);
    container.addEventListener('mousemove', handleMouseMove);
    container.addEventListener('mouseup', stopDrag);
    container.addEventListener('mouseleave', stopDrag);

    // Touch events
    container.addEventListener('touchstart', handleMouseDown, {
      passive: true,
    });
    container.addEventListener('touchmove', handleMouseMove, { passive: true });
    container.addEventListener('touchend', stopDrag);

    return () => {
      container.removeEventListener('mousedown', handleMouseDown);
      container.removeEventListener('mousemove', handleMouseMove);
      container.removeEventListener('mouseup', stopDrag);
      container.removeEventListener('mouseleave', stopDrag);

      container.removeEventListener('touchstart', handleMouseDown);
      container.removeEventListener('touchmove', handleMouseMove);
      container.removeEventListener('touchend', stopDrag);
    };
  }, [mode]);

  return (
    <div className="flex w-full flex-col gap-2" dir="rtl">
      {inputMode === 'text' ? (
        <TextInput
          label={label}
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyDown={handleKeyDown}
          disabled={disabled}
          className={className}
          placeholder={placeholder}
          name={name}
        />
      ) : (
        <div className="relative w-full" ref={selectRef}>
          {label && (
            <label className="mb-2 block font-medium text-neutral-300">
              {label}
            </label>
          )}
          <div
            className={cn(
              'relative h-[55px] w-full cursor-pointer rounded-sm border bg-[#3b3b3b] px-4 pt-3.5 pb-1 font-light text-white',
              'focus:border-primary-500 focus:ring-primary-500 focus:shadow-primary-400 border-[#3b3b3b] focus:shadow-sm focus:ring-1'
            )}
            onClick={() => setIsSelectOpen(!isSelectOpen)}
          >
            <div className="flex min-h-[1.5rem] w-full items-center">
              <span className="text-stone-500">{placeholder}</span>
            </div>
            <ChevronDown
              size={16}
              className={cn(
                'absolute top-1/2 left-4 -translate-y-1/2 transform text-stone-400 transition-transform',
                isSelectOpen && 'rotate-180'
              )}
            />
          </div>
          {isSelectOpen && (availableOptions.length > 0 || showMoreEnabled) && (
            <div className="absolute right-0 left-0 z-50 mt-1 max-h-72 overflow-auto rounded-sm border bg-[#3b3b3b] py-1 shadow-lg">
              {availableOptions.map((option) => (
                <div
                  key={option.value}
                  className="cursor-pointer p-3.5 text-sm text-white hover:bg-neutral-700"
                  onClick={() => handleSelectOption(option.value)}
                >
                  {option.label}
                </div>
              ))}

              {showMoreEnabled && (
                <>
                  {availableOptions.length > 0 && (
                    <div className="border-t border-gray-600 my-1" />
                  )}
                  <div
                    className="cursor-pointer p-3.5 text-sm text-primary-400 hover:bg-neutral-700 flex items-center gap-2"
                    onClick={handleShowMore}
                  >
                    <MoreHorizontal size={16} />
                    <span>نمایش بیشتر...</span>
                  </div>
                </>
              )}
            </div>
          )}
        </div>
      )}

      <div
        ref={containerRef}
        className={`flex gap-2 rounded select-none ${mode === 'wrap' ? 'flex-wrap' : 'cursor-grab overflow-x-auto whitespace-nowrap active:cursor-grabbing'} ${tags.length !== 0 && 'p-1'}`}
        style={{
          WebkitOverflowScrolling: 'touch',
          scrollbarWidth: 'none',
          msOverflowStyle: 'none',
        }}
      >
        <AnimatePresence initial={false}>
          {tags.map((tag) => (
            <motion.div
              key={tag}
              initial={{ opacity: 0, scale: 0.8, x: 10 }}
              animate={{ opacity: 1, scale: 1, x: 0 }}
              exit={{ opacity: 0, scale: 0.8, x: 10 }}
              transition={{ type: 'spring', stiffness: 300, damping: 20 }}
              className="flex shrink-0 items-center gap-2 rounded-full border border-neutral-400 bg-transparent py-1 pr-2 pl-1.5 text-sm"
            >
              <span className="text-neutral-400">#{tag}</span>
              <button
                onClick={() => removeTag(tag)}
                className="text-neutral-400 hover:text-neutral-600"
                type="button"
              >
                <X
                  size={20}
                  className="cursor-pointer rounded-full border-1 border-neutral-400 p-0.5"
                />
              </button>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Modal for expanded user selection */}
      {showMoreEnabled && (
        <SourceInput
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onSelect={handleModalSelect}
          users={users}
          selectedUsers={selectedUsers}
          title={modalTitle}
          searchPlaceholder={searchPlaceholder}
        />
      )}
    </div>
  );
}
