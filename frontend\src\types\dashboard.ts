export interface Dashboard {
  id: string | number;
  title: string;
  description?: string;
  preview?: string;
  params: {
    runtime: {
      gap: number;
    };
    query: {
      q: string;
      hashtags?: string[];
      sources?: string[];
    };
  };
}

export interface CreateDashboardPayload {
  title: string;
  description?: string;
  preview?: string;
  params: {
    runtime: {
      gap: number;
    };
    query: {
      q: string;
      report_type?: string;
      hashtags?: string[];
      sources?: string[];
    };
  };
}

export interface CreateDashboardResponse {
  status: string;
  code: number;
  message: string;
  data: Dashboard;
}

export interface DashboardsListResponse {
  status: string;
  code: number;
  message: string;
  data: {
    dashboards: Dashboard[];
  };
}
