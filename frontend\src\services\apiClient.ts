import axios from 'axios';

const mode = import.meta.env.MODE;

const apiClient = axios.create({
  baseURL:
    mode === 'development'
      ? import.meta.env.VITE_API_BASE_URL
      : window.env.VITE_API_BASE_URL,

});

// Flag to prevent multiple simultaneous refresh attempts
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (value?: unknown) => void;
  reject: (reason?: unknown) => void;
}> = [];

const processQueue = (error: unknown, token: string | null = null) => {
  failedQueue.forEach((prom) => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });

  failedQueue = [];
};

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token expiration and refresh
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // If the error status is 401 and we haven't already tried to refresh
    if (error.response?.status === 401 && !originalRequest._retry) {
      // Don't try to refresh token for login requests - just let them fail
      if (originalRequest.url?.includes('/account/login')) {
        return Promise.reject(error);
      }

      if (isRefreshing) {
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        })
          .then((token) => {
            originalRequest.headers['Authorization'] = `Bearer ${token}`;
            return apiClient(originalRequest);
          })
          .catch((err) => Promise.reject(err));
      }

      originalRequest._retry = true;
      isRefreshing = true;

      try {
        const refreshToken = localStorage.getItem('refreshToken');

        if (!refreshToken) {
          throw new Error('No refresh token available');
        }

        // Import refresh function dynamically to avoid circular dependency
        const { refreshAuthToken } = await import('./auth.api');

        // Attempt to refresh the token
        const newToken = await refreshAuthToken(refreshToken);

        // Update stored tokens
        localStorage.setItem('authToken', newToken);

        // Update Authorization header for original request
        originalRequest.headers['Authorization'] = `Bearer ${newToken}`;

        // Process any queued requests
        processQueue(null, newToken);

        return apiClient(originalRequest);
      } catch (refreshError) {
        // Refresh failed, logout user
        processQueue(refreshError, null);

        // Clear tokens and redirect to root page (login page)
        localStorage.removeItem('authToken');
        localStorage.removeItem('refreshToken');

        // Only redirect if we're not already on the login page
        if (window.location.pathname !== '/') {
          window.location.href = '/';
        }

        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }

    return Promise.reject(error);
  }
);

export default apiClient;
