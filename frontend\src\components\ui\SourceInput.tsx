import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Search, Users } from 'lucide-react';
import UserCard, { User } from './UserCard';
import UserDetailDrawer from './UserDetailDrawer';

interface SourceInputProps {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (users: User[]) => void;
  users: User[];
  selectedUsers: User[];
  title?: string;
  searchPlaceholder?: string;
}

export default function SourceInput({
  isOpen,
  onClose,
  onSelect,
  users,
  selectedUsers,
  title = 'انتخاب منابع',
  searchPlaceholder = 'جستجو منابع...',
}: SourceInputProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [internalSelectedUsers, setInternalSelectedUsers] = useState<User[]>(selectedUsers);
  const [selectedUserForDetails, setSelectedUserForDetails] = useState<User | null>(
    selectedUsers.length > 0 ? selectedUsers[selectedUsers.length - 1] : users.length > 0 ? users[0] : null
  );

  useEffect(() => {
    setInternalSelectedUsers(selectedUsers);
    // Show the last selected user's details, or first user if none selected
    if (selectedUsers.length > 0) {
      setSelectedUserForDetails(selectedUsers[selectedUsers.length - 1]);
    } else if (users.length > 0 && !selectedUserForDetails) {
      setSelectedUserForDetails(users[0]);
    }
  }, [selectedUsers, users]);

  // Filter users based on search query
  const filteredUsers = users.filter(
    (user) =>
      user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.username.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleUserSelect = (user: User) => {
    const isSelected = internalSelectedUsers.some((u) => u.id === user.id);
    let newSelection: User[];

    if (isSelected) {
      newSelection = internalSelectedUsers.filter((u) => u.id !== user.id);
      // If we're removing the currently displayed user, show the last remaining selected user or first user
      if (selectedUserForDetails?.id === user.id) {
        if (newSelection.length > 0) {
          setSelectedUserForDetails(newSelection[newSelection.length - 1]);
        } else if (users.length > 0) {
          setSelectedUserForDetails(users[0]);
        }
      }
    } else {
      newSelection = [...internalSelectedUsers, user];
      // Show the newly selected user's details
      setSelectedUserForDetails(user);
    }

    setInternalSelectedUsers(newSelection);
  };

  const handleConfirm = () => {
    onSelect(internalSelectedUsers);
    onClose();
  };

  const handleCancel = () => {
    setInternalSelectedUsers(selectedUsers);
    // Reset to show last selected user or first user
    if (selectedUsers.length > 0) {
      setSelectedUserForDetails(selectedUsers[selectedUsers.length - 1]);
    } else if (users.length > 0) {
      setSelectedUserForDetails(users[0]);
    }
    onClose();
  };

  const handleViewUserDetails = (user: User) => {
    setSelectedUserForDetails(user);
  };

  // Close modal on Escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        handleCancel();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          {/* Backdrop */}
          <motion.div
            className="absolute inset-0 bg-black/70 backdrop-blur-sm"
            onClick={handleCancel}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          />

          {/* Modal Content */}
          <motion.div
            className="relative mx-auto h-[90vh] w-full max-w-7xl overflow-hidden rounded-lg border-2 border-primary-400/50 bg-gradient-to-br from-gray-900/95 to-black/95 shadow-2xl shadow-primary-400/20 flex"
            initial={{
              opacity: 0,
              scale: 0.8,
              y: 50,
            }}
            animate={{
              opacity: 1,
              scale: 1,
              y: 0,
            }}
            exit={{
              opacity: 0,
              scale: 0.8,
              y: 50,
            }}
            transition={{
              type: 'spring',
              stiffness: 300,
              damping: 25,
              duration: 0.4,
            }}
          >
            {/* Futuristic border glow effect */}
            <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-transparent via-primary-400/10 to-transparent opacity-50" />

            {/* Left Panel - Users Grid */}
            <div className="flex-1 flex flex-col">
              {/* Header */}
              <div className="relative border-b border-primary-400/30 bg-gradient-to-r from-gray-800/50 to-gray-900/50 p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary-400/20 border border-primary-400/50">
                      <Users size={20} className="text-primary-400" />
                    </div>
                    <div>
                      <h2 className="text-xl font-bold text-white">{title}</h2>
                      <p className="text-sm text-gray-400">
                        {internalSelectedUsers.length} منبع انتخاب شده
                      </p>
                    </div>
                  </div>

                  <button
                    onClick={handleCancel}
                    className="flex h-10 w-10 items-center justify-center rounded-full border border-gray-600 bg-gray-800/50 text-gray-400 transition-all duration-200 hover:border-red-400 hover:bg-red-400/10 hover:text-red-400"
                  >
                    <X size={20} />
                  </button>
                </div>

                {/* Search Bar */}
                <div className="mt-4 relative">
                  <div className="relative">
                    <Search size={18} className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      placeholder={searchPlaceholder}
                      className="w-full rounded-lg border border-gray-600/50 bg-gray-800/50 py-3 pr-10 pl-4 text-white placeholder-gray-400 focus:border-primary-400/50 focus:outline-none focus:ring-2 focus:ring-primary-400/20"
                      dir="rtl"
                    />
                  </div>
                </div>
              </div>

              {/* Users Grid */}
              <div className="flex-1 overflow-y-auto p-6">
                <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-4 xl:grid-cols-5">
                  {filteredUsers.map((user, index) => (
                    <motion.div
                      key={user.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.05, duration: 0.3 }}
                    >
                      <UserCard
                        user={{
                          ...user,
                          isSelected: internalSelectedUsers.some((u) => u.id === user.id),
                        }}
                        onSelect={handleUserSelect}
                        onViewDetails={handleViewUserDetails}
                      />
                    </motion.div>
                  ))}
                </div>

                {filteredUsers.length === 0 && (
                  <div className="flex h-64 items-center justify-center">
                    <div className="text-center">
                      <Users size={48} className="mx-auto mb-4 text-gray-600" />
                      <p className="text-gray-400">منبعی یافت نشد</p>
                    </div>
                  </div>
                )}
              </div>

              {/* Footer */}
              <div className="border-t border-primary-400/30 bg-gradient-to-r from-gray-800/50 to-gray-900/50 p-6">
                <div className="flex justify-end gap-4">
                  <button
                    onClick={handleCancel}
                    className="rounded-lg border border-gray-600 bg-gray-800/50 px-6 py-3 text-gray-300 transition-all duration-200 hover:border-gray-500 hover:bg-gray-700/50"
                  >
                    انصراف
                  </button>
                  <button
                    onClick={handleConfirm}
                    className="rounded-lg bg-primary-500 px-6 py-3 text-white transition-all duration-200 hover:bg-primary-600 focus:ring-2 focus:ring-primary-400/50"
                  >
                    تأیید ({internalSelectedUsers.length})
                  </button>
                </div>
              </div>
            </div>

            {/* Right Panel - Always Visible Detail Drawer */}
            <div className="w-80 border-l-2 border-primary-400/30 bg-gradient-to-b from-gray-900/98 to-black/98">
              <UserDetailDrawer
                isOpen={true}
                onClose={() => {}} // No close functionality since it's always visible
                user={selectedUserForDetails}
                onSelect={handleUserSelect}
                isSelected={selectedUserForDetails ? internalSelectedUsers.some((u) => u.id === selectedUserForDetails.id) : false}
              />
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
