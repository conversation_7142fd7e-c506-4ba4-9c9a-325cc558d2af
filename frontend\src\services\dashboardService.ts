import apiClient from './apiClient';
import { CreateDashboardPayload, CreateDashboardResponse, Dashboard, DashboardsListResponse } from '@/types/dashboard';
import * as z from 'zod';

// Zod schema for validating the create dashboard payload
export const createDashboardSchema = z.object({
  title: z.string().min(3, { message: 'عنوان باید حداقل ۳ کاراکتر داشته باشد.' }),
  description: z.string().optional(),
  params: z.object({
    runtime: z.object({
      gap: z.number().positive({ message: 'بازه زمانی باید مثبت باشد.' }),
    }),
    query: z.object({
      q: z.string().min(2, { message: 'عبارت جستجو الزامی است.' }),
      hashtags: z.array(z.string()).optional(),
      sources: z.array(z.string()).optional(),
    }),
  }),
});

export const createDashboard = async (payload: CreateDashboardPayload): Promise<Dashboard> => {
  try {
    // Validate payload with Zod
    const validatedPayload = createDashboardSchema.parse(payload);

    const response = await apiClient.post<CreateDashboardResponse>('/dashboard/', validatedPayload);

    if (!response.data.data) {
      throw new Error('Invalid response format');
    }

    return response.data.data;
  } catch (error: unknown) {
    console.error('Create dashboard error:', error);

    // Handle Zod validation errors
    if (error instanceof z.ZodError) {
      const errorMessages = error.issues.map(issue => issue.message).join(', ');
      throw new Error(`خطا در اعتبارسنجی داده‌ها: ${errorMessages}`);
    }

    // Handle different types of API errors
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response: { status: number; data?: any } };
      if (axiosError.response?.status === 400) {
        throw new Error('اطلاعات وارد شده نامعتبر است');
      } else if (axiosError.response?.status === 401) {
        throw new Error('شما مجاز به انجام این عملیات نیستید');
      } else if (axiosError.response?.status === 500) {
        throw new Error('خطا در سرور. لطفا بعدا تلاش کنید');
      }
    }

    if (error && typeof error === 'object' && 'code' in error) {
      const networkError = error as { code: string };
      if (networkError.code === 'NETWORK_ERROR') {
        throw new Error('خطا در اتصال به سرور');
      }
    }

    throw new Error('خطا در ایجاد داشبورد');
  }
};

// Get all dashboards
export const getDashboards = async (): Promise<Dashboard[]> => {
  try {
    const response = await apiClient.get<DashboardsListResponse>('/dashboard/');

    if (!response.data.data) {
      throw new Error('Invalid response format');
    }

    return response.data.data.dashboards;
  } catch (error: unknown) {
    console.error('Get dashboards error:', error);

    // Handle different types of API errors
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response: { status: number; data?: any } };
      if (axiosError.response?.status === 401) {
        throw new Error('شما مجاز به مشاهده داشبوردها نیستید');
      } else if (axiosError.response?.status === 500) {
        throw new Error('خطا در سرور. لطفا بعدا تلاش کنید');
      }
    }

    if (error && typeof error === 'object' && 'code' in error) {
      const networkError = error as { code: string };
      if (networkError.code === 'NETWORK_ERROR') {
        throw new Error('خطا در اتصال به سرور');
      }
    }

    throw new Error('خطا در دریافت لیست داشبوردها');
  }
};

// Get dashboard by ID
export const getDashboardById = async (id: string | number): Promise<Dashboard> => {
  try {
    const response = await apiClient.get<CreateDashboardResponse>(`/dashboard/${id}/`);

    if (!response.data.data) {
      throw new Error('Invalid response format');
    }

    return response.data.data;
  } catch (error: unknown) {
    console.error('Get dashboard by ID error:', error);

    // Handle different types of API errors
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response: { status: number; data?: any } };
      if (axiosError.response?.status === 404) {
        throw new Error('داشبورد مورد نظر یافت نشد');
      } else if (axiosError.response?.status === 401) {
        throw new Error('شما مجاز به مشاهده این داشبورد نیستید');
      } else if (axiosError.response?.status === 500) {
        throw new Error('خطا در سرور. لطفا بعدا تلاش کنید');
      }
    }

    if (error && typeof error === 'object' && 'code' in error) {
      const networkError = error as { code: string };
      if (networkError.code === 'NETWORK_ERROR') {
        throw new Error('خطا در اتصال به سرور');
      }
    }

    throw new Error('خطا در دریافت اطلاعات داشبورد');
  }
};

// Delete dashboard
export const deleteDashboard = async (id: string | number): Promise<void> => {
  try {
    await apiClient.delete(`/dashboard/${id}/`);
  } catch (error: unknown) {
    console.error('Delete dashboard error:', error);

    // Handle different types of API errors
    if (error && typeof error === 'object' && 'response' in error) {
      const axiosError = error as { response: { status: number; data?: any } };
      if (axiosError.response?.status === 404) {
        throw new Error('داشبورد مورد نظر یافت نشد');
      } else if (axiosError.response?.status === 401) {
        throw new Error('شما مجاز به حذف این داشبورد نیستید');
      } else if (axiosError.response?.status === 500) {
        throw new Error('خطا در سرور. لطفا بعدا تلاش کنید');
      }
    }

    if (error && typeof error === 'object' && 'code' in error) {
      const networkError = error as { code: string };
      if (networkError.code === 'NETWORK_ERROR') {
        throw new Error('خطا در اتصال به سرور');
      }
    }

    throw new Error('خطا در حذف داشبورد');
  }
};