import { useState } from 'react';
import TagInput, { SelectOption } from './TagInput';
import { User } from './UserCard';

// Sample data for demonstration
const sampleOptions: SelectOption[] = [
  { value: 'technology', label: 'فناوری' },
  { value: 'design', label: 'طراحی' },
  { value: 'marketing', label: 'بازاریابی' },
  { value: 'business', label: 'کسب و کار' },
  { value: 'development', label: 'توسعه' },
];

const sampleUsers: User[] = [
  {
    id: '1',
    name: 'احمد زمانی پور',
    username: 'ahmad.zamani',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    isOnline: true,
    bio: 'روزنامه‌نگار و تحلیلگر سیاسی با بیش از ۱۰ سال تجربه در حوزه رسانه',
    email: '<EMAIL>',
    location: 'تهران، ایران',
    joinDate: '2015-03-20',
    stats: { posts: 1250, followers: 15000, following: 500, articles: 320 },
  },
  {
    id: '2',
    name: 'فاطمه محمدی',
    username: 'fateme.mohammadi',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    isOnline: false,
    lastSeen: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
    bio: 'خبرنگار اقتصادی و کارشناس بازار سرمایه',
    email: '<EMAIL>',
    location: 'اصفهان، ایران',
    joinDate: '2018-07-15',
    stats: { posts: 890, followers: 8500, following: 320, articles: 180 },
  },
  {
    id: '3',
    name: 'علی رضایی',
    username: 'ali.rezaei',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    isOnline: true,
    bio: 'گزارشگر ورزشی و تحلیلگر فوتبال',
    email: '<EMAIL>',
    website: 'https://alirezaei.blog',
    location: 'مشهد، ایران',
    joinDate: '2016-11-08',
    stats: { posts: 2100, followers: 25000, following: 800, articles: 450 },
  },
  {
    id: '4',
    name: 'مریم کریمی',
    username: 'maryam.karimi',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    isOnline: true,
    bio: 'روزنامه‌نگار فرهنگی و منتقد هنری',
    email: '<EMAIL>',
    location: 'شیراز، ایران',
    joinDate: '2017-05-12',
    stats: { posts: 1680, followers: 12000, following: 600, articles: 280 },
  },
  {
    id: '5',
    name: 'حسین احمدی',
    username: 'hossein.ahmadi',
    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
    isOnline: false,
    lastSeen: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    bio: 'خبرنگار علمی و فناوری، متخصص حوزه هوش مصنوعی',
    email: '<EMAIL>',
    website: 'https://tech-news.ir',
    location: 'تهران، ایران',
    joinDate: '2014-09-30',
    stats: { posts: 3200, followers: 35000, following: 1200, articles: 680 },
  },
  {
    id: '6',
    name: 'زهرا صادقی',
    username: 'zahra.sadeghi',
    avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face',
    isOnline: true,
    bio: 'گزارشگر محیط زیست و تغییرات اقلیمی',
    email: '<EMAIL>',
    location: 'تبریز، ایران',
    joinDate: '2019-01-25',
    stats: { posts: 750, followers: 6800, following: 280, articles: 150 },
  },
];

export default function TagInputDemo() {
  const [textTags, setTextTags] = useState<string[]>([]);
  const [selectTags, setSelectTags] = useState<string[]>([]);
  const [userTags, setUserTags] = useState<string[]>([]);

  return (
    <div className="min-h-screen bg-black p-8 space-y-12" dir="rtl">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8 text-center">
          نمایش TagInput پیشرفته با Side Drawer
        </h1>

        {/* Text Input Mode */}
        <div className="space-y-4 p-6 rounded-lg border border-gray-700 bg-gray-900/50">
          <h2 className="text-xl font-semibold text-white">حالت ورودی متن</h2>
          <p className="text-gray-400">
            برای اضافه کردن تگ، متن را تایپ کرده و Enter، کاما یا فاصله بزنید
          </p>
          <TagInput
            label="تگ‌های سفارشی"
            value={textTags}
            onChange={setTextTags}
            inputMode="text"
            placeholder="تگ‌های خود را تایپ کنید..."
            mode="wrap"
          />
          <div className="text-sm text-gray-400">
            تگ‌های فعلی: {textTags.join(', ') || 'هیچ کدام'}
          </div>
        </div>

        {/* Select Mode */}
        <div className="space-y-4 p-6 rounded-lg border border-gray-700 bg-gray-900/50">
          <h2 className="text-xl font-semibold text-white">حالت انتخاب</h2>
          <p className="text-gray-400">
            از گزینه‌های از پیش تعریف شده انتخاب کنید
          </p>
          <TagInput
            label="دسته‌بندی‌ها"
            value={selectTags}
            onChange={setSelectTags}
            inputMode="select"
            options={sampleOptions}
            placeholder="دسته‌بندی انتخاب کنید..."
            mode="wrap"
          />
          <div className="text-sm text-gray-400">
            دسته‌بندی‌های انتخاب شده: {selectTags.join(', ') || 'هیچ کدام'}
          </div>
        </div>

        {/* Enhanced Select Mode with Modal and Side Drawer */}
        <div className="space-y-4 p-6 rounded-lg border border-primary-400/30 bg-gradient-to-br from-gray-900/70 to-black/70">
          <h2 className="text-xl font-semibold text-primary-400">
            حالت انتخاب پیشرفته با مودال و Side Drawer
          </h2>
          <p className="text-gray-400">
            انتخاب از گزینه‌های محدود + دکمه "نمایش بیشتر" برای باز کردن مودال منابع با قابلیت مشاهده جزئیات
          </p>
          <TagInput
            label="انتخاب منابع"
            value={userTags}
            onChange={setUserTags}
            inputMode="select"
            options={sampleOptions.slice(0, 3)} // Show only first 3 options in dropdown
            users={sampleUsers}
            showMoreEnabled={true}
            modalTitle="انتخاب منابع سیستم"
            searchPlaceholder="جستجو در منابع..."
            placeholder="منابع را انتخاب کنید..."
            mode="wrap"
          />
          <div className="text-sm text-gray-400">
            منابع انتخاب شده: {userTags.join(', ') || 'هیچ کدام'}
          </div>
        </div>

        {/* Usage Instructions */}
        <div className="p-6 rounded-lg border border-blue-500/30 bg-blue-900/10">
          <h3 className="text-lg font-semibold text-blue-400 mb-4">راهنمای استفاده</h3>
          <ul className="space-y-2 text-gray-300 text-sm">
            <li>• <strong>حالت متن:</strong> تایپ کرده و Enter، کاما یا فاصله بزنید</li>
            <li>• <strong>حالت انتخاب:</strong> روی فیلد کلیک کرده و از گزینه‌ها انتخاب کنید</li>
            <li>• <strong>نمایش بیشتر:</strong> روی "نمایش بیشتر..." کلیک کنید تا مودال باز شود</li>
            <li>• <strong>مشاهده جزئیات:</strong> روی هر کارت کاربر کلیک کنید تا Side Drawer باز شود</li>
            <li>• <strong>انتخاب/لغو انتخاب:</strong> روی دکمه کوچک در گوشه کارت کلیک کنید</li>
            <li>• <strong>حذف تگ:</strong> روی آیکون X کنار هر تگ کلیک کنید</li>
            <li>• <strong>جستجو در مودال:</strong> از نوار جستجو برای یافتن کاربران استفاده کنید</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
