import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';

export const PublicRoute: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const { isAuthenticated, isLoading } = useAuth();

  // Check if authentication should be skipped
  const skipAuth = import.meta.env.VITE_SKIP_AUTH === 'true';

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="border-primary-500 h-32 w-32 animate-spin rounded-full border-b-2"></div>
      </div>
    );
  }

  // If skipping auth, allow access to public routes (like login page)
  if (skipAuth) {
    return <>{children}</>;
  }

  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }

  return <>{children}</>;
};
