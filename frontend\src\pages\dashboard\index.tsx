import { useState, useEffect } from 'react';
import DashboardCard from '@/pages/dashboard/components/DashboardCard';
import CreateDashboardCard from '@/pages/dashboard/components/CreateDashboardCard';
import { getDashboards } from '@/services/dashboardService';
import { Dashboard } from '@/types/dashboard';

export default function Page() {
  const [dashboards, setDashboards] = useState<Dashboard[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboards = async () => {
      try {
        setLoading(true);
        const data = await getDashboards();
        setDashboards(data);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'خطا در دریافت داشبوردها';
        setError(errorMessage);
        console.error('Error fetching dashboards:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboards();
  }, []);

  const handleDashboardDeleted = (deletedId: string | number) => {
    setDashboards(prev => prev.filter(dashboard => dashboard.id !== deletedId));
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4">
        <div className="mt-8 flex justify-center">
          <div className="text-white">در حال بارگذاری...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4">
        <div className="mt-8 flex justify-center">
          <div className="text-red-400">{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4">
      <div className="mt-8 grid grid-cols-1 place-items-center gap-4 text-white sm:grid-cols-1 md:grid-cols-2 md:gap-6 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 2xl:gap-8">
        <CreateDashboardCard />
        {dashboards.map((dashboard, index) => (
          <DashboardCard
            key={dashboard.id}
            dashboard={dashboard}
            index={index}
            onDeleted={handleDashboardDeleted}
          />
        ))}
      </div>
    </div>
  );
}
