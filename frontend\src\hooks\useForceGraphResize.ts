import { useEffect, useRef, useCallback } from 'react';
import { ForceGraphMethods } from 'react-force-graph-2d';

interface UseForceGraphResizeOptions {
  /**
   * Debounce delay in milliseconds for resize events
   * @default 100
   */
  debounceDelay?: number;
  /**
   * Whether to maintain aspect ratio when resizing
   * @default false
   */
  maintainAspectRatio?: boolean;
  /**
   * Minimum height for the graph
   * @default 200
   */
  minHeight?: number;
  /**
   * Maximum height for the graph
   * @default undefined
   */
  maxHeight?: number;
  /**
   * Whether to auto-fit graph to container after resize
   * @default true
   */
  autoFitOnResize?: boolean;
}

/**
 * Custom hook for making ForceGraph2D responsive to container size changes
 * This hook automatically adjusts graph dimensions to match its container
 * and handles resize events with debouncing for better performance
 */
export const useForceGraphResize = (
  graphRef: React.RefObject<ForceGraphMethods | undefined>,
  options: UseForceGraphResizeOptions = {}
) => {
  const {
    debounceDelay = 100,
    maintainAspectRatio = false,
    minHeight = 200,
    maxHeight,
    autoFitOnResize = true,
  } = options;

  const containerRef = useRef<HTMLDivElement>(null);
  const resizeTimeoutRef = useRef<NodeJS.Timeout>();
  const resizeObserverRef = useRef<ResizeObserver>();
  const dimensionsRef = useRef({ width: 0, height: 0 });

  const resizeGraph = useCallback(() => {
    if (!containerRef.current) return;

    const container = containerRef.current;
    const containerRect = container.getBoundingClientRect();

    let newHeight = containerRect.height;
    let newWidth = containerRect.width;

    // Apply height constraints
    if (newHeight < minHeight) {
      newHeight = minHeight;
    }
    if (maxHeight && newHeight > maxHeight) {
      newHeight = maxHeight;
    }

    // Calculate width if maintaining aspect ratio
    if (maintainAspectRatio && dimensionsRef.current.height > 0) {
      const aspectRatio =
        dimensionsRef.current.width / dimensionsRef.current.height;
      newWidth = newHeight * aspectRatio;
    }

    // Store new dimensions
    dimensionsRef.current = { width: newWidth, height: newHeight };

    // Update graph size
    try {
      // ForceGraph2D automatically handles canvas resizing when container size changes
      // We just need to trigger a re-render and optionally auto-fit
      if (autoFitOnResize && graphRef.current) {
        // Small delay to ensure the canvas has resized
        setTimeout(() => {
          if (graphRef.current) {
            graphRef.current.zoomToFit(400);
          }
        }, 150);
      }
    } catch (error) {
      console.warn('Error resizing force graph:', error);
    }
  }, [graphRef, minHeight, maxHeight, maintainAspectRatio, autoFitOnResize]);

  const debouncedResize = useCallback(() => {
    if (resizeTimeoutRef.current) {
      clearTimeout(resizeTimeoutRef.current);
    }

    resizeTimeoutRef.current = setTimeout(() => {
      resizeGraph();
    }, debounceDelay);
  }, [resizeGraph, debounceDelay]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Create ResizeObserver to watch container size changes
    resizeObserverRef.current = new ResizeObserver((entries) => {
      for (const entry of entries) {
        if (entry.target === container) {
          debouncedResize();
        }
      }
    });

    // Start observing the container
    resizeObserverRef.current.observe(container);

    // Initial resize
    setTimeout(() => {
      resizeGraph();
    }, 50);

    // Cleanup
    return () => {
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }
    };
  }, [debouncedResize, resizeGraph]);

  // Handle window resize as fallback
  useEffect(() => {
    const handleWindowResize = () => {
      debouncedResize();
    };

    window.addEventListener('resize', handleWindowResize);
    return () => {
      window.removeEventListener('resize', handleWindowResize);
    };
  }, [debouncedResize]);

  return {
    containerRef,
    resizeGraph,
    dimensions: dimensionsRef.current,
  };
};

export default useForceGraphResize;
